#!/usr/bin/env python3
"""
Fix numpy compatibility issues.

This script patches numpy to ensure compatibility with pandas.
Run this before running tests or the main application.
"""

import sys
import importlib


def patch_numpy():
    """Patch numpy to add missing __version__ attribute."""
    # First, make sure numpy is imported
    import numpy as np
    
    # Check if __version__ is missing
    if not hasattr(np, '__version__'):
        print("Patching numpy to add __version__ attribute...")
        
        # Try to get version from different sources
        version_found = None
        
        # Method 1: Try importlib.metadata
        try:
            if sys.version_info >= (3, 8):
                from importlib.metadata import version
            else:
                from importlib_metadata import version
            version_found = version('numpy')
            print(f"Found numpy version from metadata: {version_found}")
        except:
            pass
        
        # Method 2: Try pkg_resources
        if not version_found:
            try:
                import pkg_resources
                version_found = pkg_resources.get_distribution('numpy').version
                print(f"Found numpy version from pkg_resources: {version_found}")
            except:
                pass
        
        # Method 3: Look for version info in numpy itself
        if not version_found:
            try:
                if hasattr(np, 'version'):
                    if hasattr(np.version, 'version'):
                        version_found = np.version.version
                    elif hasattr(np.version, 'full_version'):
                        version_found = np.version.full_version
                    print(f"Found numpy version from np.version: {version_found}")
            except:
                pass
        
        # Method 4: Default fallback
        if not version_found:
            version_found = '1.24.0'  # Safe default version
            print(f"Using fallback numpy version: {version_found}")
        
        # Set the version
        np.__version__ = version_found
        print(f"Patched numpy.__version__ = {np.__version__}")
    else:
        print(f"numpy.__version__ already exists: {np.__version__}")
    
    return np.__version__


if __name__ == "__main__":
    print("NumPy Compatibility Patcher")
    print("=" * 40)
    
    # Patch numpy
    version = patch_numpy()
    
    # Test imports
    print("\nTesting imports...")
    try:
        import numpy as np
        print(f"✓ NumPy imported, version: {np.__version__}")
        
        import pandas as pd
        print(f"✓ Pandas imported, version: {pd.__version__}")
        
        # Test basic functionality
        df = pd.DataFrame({'a': [1, 2, 3]})
        arr = np.array([1, 2, 3])
        
        print("✓ Basic functionality works")
        print("\n✅ Patching successful!")
        
    except Exception as e:
        print(f"\n❌ Error after patching: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)