#!/bin/bash
# Run tests with Python 3.9 environment

# Get the directory of this script
SCRIPT_DIR="$( cd "$( dirname "${BASH_SOURCE[0]}" )" && pwd )"
cd "$SCRIPT_DIR"

# Check if venv exists
if [ ! -d "venv_3.9" ]; then
    echo "Creating Python 3.9 virtual environment..."
    python3.9 -m venv venv_3.9
fi

# Activate virtual environment
source venv_3.9/bin/activate

# Ensure pip is up to date
python -m pip install --upgrade pip

# Install compatible numpy/pandas first
echo "Installing compatible numpy and pandas..."
python -m pip install numpy==1.24.3 pandas==2.0.3

# Install other requirements
echo "Installing other requirements..."
python -m pip install -r requirements.txt

# Run tests
echo ""
echo "Running tests..."
python scripts/testing/run_all_tests.py "$@"