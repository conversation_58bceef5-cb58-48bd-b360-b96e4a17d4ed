#!/usr/bin/env python3
"""
Example demonstrating the monitoring storage backend functionality.

This script shows how to:
1. Configure persistent storage for metrics
2. Track function performance with automatic storage
3. Query historical metrics
4. Generate summary reports
"""
# Standard library imports
import time
from datetime import datetime, timedelta

# Third-party imports
import pandas as pd
import numpy as np

# Project imports
from yemen_trade_diagnostic.monitoring.performance_monitor import (
    get_performance_monitor, monitor_performance
)
from yemen_trade_diagnostic.monitoring.storage import (
    MetricsStorage, create_storage_backend
)
from yemen_trade_diagnostic.interfaces.logging_interface import get_logger

logger = get_logger(__name__)


# Example functions to monitor
@monitor_performance(track_args=True, track_result=True)
def process_data(size: int = 1000):
    """Simulate data processing with variable size."""
    # Create some data
    df = pd.DataFrame({
        'x': np.random.randn(size),
        'y': np.random.randn(size),
        'z': np.random.randn(size)
    })
    
    # Simulate processing
    time.sleep(np.random.uniform(0.1, 0.5))
    
    # Compute some results
    result = df.describe()
    
    # Randomly fail sometimes
    if np.random.random() < 0.1:
        raise ValueError("Simulated processing error")
    
    return result


@monitor_performance()
def memory_intensive_task():
    """Simulate a memory-intensive operation."""
    # Allocate large array
    large_array = np.random.randn(5000, 5000)
    
    # Do some computation
    result = np.dot(large_array, large_array.T)
    
    time.sleep(0.2)
    
    return result.sum()


@monitor_performance(track_args=True)
def batch_operation(batch_id: int, items: int = 100):
    """Simulate batch processing."""
    processed = 0
    
    for i in range(items):
        # Simulate item processing
        time.sleep(0.01)
        processed += 1
    
    return {'batch_id': batch_id, 'processed': processed}


def main():
    """Main demonstration function."""
    print("=== Monitoring Storage Backend Demo ===\n")
    
    # 1. Initialize monitor with SQLite storage
    print("1. Initializing performance monitor with SQLite storage...")
    monitor = get_performance_monitor(
        storage_backend="sqlite",
        storage_config={"db_path": "output/metrics/demo_metrics.db"}
    )
    print("✓ Monitor initialized\n")
    
    # 2. Run some monitored operations
    print("2. Running monitored operations...")
    
    # Process data with different sizes
    for size in [1000, 5000, 10000]:
        try:
            result = process_data(size=size)
            print(f"✓ Processed data with size {size}")
        except Exception as e:
            print(f"✗ Failed to process data with size {size}: {e}")
    
    # Run memory intensive tasks
    for i in range(3):
        try:
            memory_intensive_task()
            print(f"✓ Completed memory intensive task {i+1}")
        except Exception as e:
            print(f"✗ Memory intensive task {i+1} failed: {e}")
    
    # Run batch operations
    for batch_id in range(5):
        try:
            batch_operation(batch_id, items=50 + batch_id * 10)
            print(f"✓ Completed batch {batch_id}")
        except Exception as e:
            print(f"✗ Batch {batch_id} failed: {e}")
    
    print()
    
    # 3. Query stored metrics
    print("3. Querying stored metrics...")
    
    if monitor.storage:
        # Load recent metrics
        recent_metrics = monitor.storage.load_metrics(limit=10)
        print(f"✓ Loaded {len(recent_metrics)} recent metrics")
        
        # Get summary statistics
        stats = monitor.storage.get_summary_stats()
        print("\nSummary Statistics:")
        for func_name, func_stats in stats.items():
            print(f"\n  {func_name}:")
            print(f"    Total executions: {func_stats['total_executions']}")
            print(f"    Success rate: {func_stats['success_rate']:.1%}")
            print(f"    Avg execution time: {func_stats['avg_execution_time']:.3f}s")
            print(f"    Max memory delta: {func_stats['max_memory_delta']:.1f}MB")
    
    # 4. Export metrics to DataFrame
    print("\n4. Exporting metrics to DataFrame...")
    
    if monitor.storage:
        df = monitor.storage.export_to_dataframe()
        print(f"✓ Exported {len(df)} metrics to DataFrame")
        
        if not df.empty:
            print("\nDataFrame Summary:")
            print(df.groupby('function_name').agg({
                'execution_time': ['count', 'mean', 'std', 'min', 'max'],
                'memory_delta': ['mean', 'max'],
                'success': 'mean'
            }).round(3))
    
    # 5. Demonstrate JSON storage backend
    print("\n5. Testing JSON storage backend...")
    
    json_backend = create_storage_backend(
        "json",
        json_path="output/metrics/demo_metrics.json"
    )
    json_storage = MetricsStorage(json_backend)
    
    # Save some metrics to JSON
    if monitor.metrics_history:
        all_metrics = []
        for func_metrics in monitor.metrics_history.values():
            all_metrics.extend(func_metrics)
        
        json_storage.save_metrics(all_metrics[:5])  # Save first 5
        print(f"✓ Saved {min(5, len(all_metrics))} metrics to JSON")
        
        # Load them back
        loaded = json_storage.load_metrics()
        print(f"✓ Loaded {len(loaded)} metrics from JSON")
    
    # 6. Demonstrate time-based queries
    print("\n6. Time-based metric queries...")
    
    if monitor.storage:
        # Get metrics from last hour
        one_hour_ago = datetime.now() - timedelta(hours=1)
        recent = monitor.storage.load_metrics(start_time=one_hour_ago)
        print(f"✓ Found {len(recent)} metrics from the last hour")
        
        # Get summary for last 30 minutes
        stats_30min = monitor.storage.get_summary_stats(
            time_window=timedelta(minutes=30)
        )
        print(f"✓ Generated summary for {len(stats_30min)} functions in last 30 minutes")
    
    print("\n✓ Demo completed successfully!")
    print("\nMetrics have been stored in:")
    print("  - SQLite: output/metrics/demo_metrics.db")
    print("  - JSON: output/metrics/demo_metrics.json")


if __name__ == "__main__":
    main()