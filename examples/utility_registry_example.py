#!/usr/bin/env python3
"""
Example of using the utility registry system.

This example demonstrates how to:
1. Register utility functions
2. Search for functions
3. Check for duplicates
4. Generate reports
"""

import sys
from pathlib import Path

# Add project root to path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root / "src"))

from yemen_trade_diagnostic.utils import (
    get_registry,
    register_function,
    validate_no_duplicates
)


# Example of registering a new utility function
@register_function(
    category="Data Processing",
    description="Process trade data with optimizations"
)
def process_trade_data(df):
    """Process trade data with memory optimization."""
    # Implementation here
    pass


@register_function(
    category="Data Processing", 
    description="Validate trade data format",
    aliases=["check_trade_data", "validate_data"]
)
def validate_trade_data(df):
    """Validate that trade data meets required format."""
    # Implementation here
    pass


def main():
    """Demonstrate utility registry usage."""
    print("Yemen Trade Diagnostic - Utility Registry Example")
    print("=" * 50)
    
    # Get the registry
    registry = get_registry()
    
    # 1. Show registry statistics
    stats = registry.get_stats()
    print("\n1. Registry Statistics:")
    print(f"   Total functions: {stats['total_functions']}")
    print(f"   Categories: {stats['categories']}")
    print(f"   Aliases: {stats['aliases']}")
    
    # 2. Search for functions
    print("\n2. Searching for functions:")
    
    # Search by name
    search_term = "dataframe"
    results = registry.search(search_term)
    print(f"\n   Functions containing '{search_term}':")
    for func in results[:5]:  # Show first 5
        print(f"   - {func.name} ({func.category}): {func.description}")
    
    # 3. Get functions by category
    print("\n3. Functions by category:")
    categories = registry.get_categories()
    for category in sorted(categories)[:3]:  # Show first 3 categories
        functions = registry.get_by_category(category)
        print(f"\n   {category}:")
        for func in functions[:3]:  # Show first 3 functions
            print(f"   - {func.name}: {func.description}")
    
    # 4. Check for duplicates
    print("\n4. Checking for duplicates:")
    if validate_no_duplicates():
        print("   ✓ No duplicate functions found")
    else:
        duplicates = registry.check_duplicates()
        print(f"   ✗ Found {len(duplicates)} duplicates")
    
    # 5. Check deprecated functions
    print("\n5. Deprecated functions:")
    deprecated = registry.get_deprecated()
    if deprecated:
        for func in deprecated:
            print(f"   - {func.name}")
            if func.replacement:
                print(f"     Replace with: {func.replacement}")
    else:
        print("   No deprecated functions")
    
    # 6. Lookup function by alias
    print("\n6. Function lookup by alias:")
    alias = "check_trade_data"
    func_info = registry.get(alias)
    if func_info:
        print(f"   '{alias}' resolves to: {func_info.name}")
        print(f"   Description: {func_info.description}")
    
    print("\n✓ Registry example complete!")


if __name__ == "__main__":
    main()