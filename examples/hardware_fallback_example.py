#!/usr/bin/env python3
"""
Example demonstrating the universal hardware acceleration with fallback support.

This script shows how the system automatically handles different platforms:
- Apple Silicon: Uses Metal GPU and Neural Engine acceleration
- Intel/AMD x86: Uses optimized BLAS libraries and SIMD instructions
- ARM (non-Apple): Uses NEON instructions and optimized libraries
- Any platform: Falls back to optimized software implementations
"""
# Standard library imports
import time
import platform

# Third-party imports
import numpy as np
import pandas as pd

# Project imports
from yemen_trade_diagnostic.hardware import get_universal_accelerator
from yemen_trade_diagnostic.interfaces.hardware_interface import WorkloadType
from yemen_trade_diagnostic.interfaces.logging_interface import get_logger

logger = get_logger(__name__)


def demonstrate_matrix_operations():
    """Demonstrate matrix multiplication acceleration."""
    print("\n=== Matrix Operations Demo ===")
    
    # Get universal accelerator
    accelerator = get_universal_accelerator()
    
    # Create test matrices
    sizes = [100, 500, 1000]
    
    for size in sizes:
        print(f"\nMatrix size: {size}x{size}")
        
        # Create random matrices
        a = np.random.randn(size, size).astype(np.float32)
        b = np.random.randn(size, size).astype(np.float32)
        
        # Baseline: NumPy multiplication
        start = time.time()
        result_baseline = a @ b
        baseline_time = time.time() - start
        print(f"  Baseline (NumPy): {baseline_time:.3f}s")
        
        # Accelerated multiplication
        start = time.time()
        result_accel = accelerator.accelerate(
            'matrix_multiply',
            WorkloadType.MATRIX_OPERATIONS,
            a,
            b=b
        )
        accel_time = time.time() - start
        print(f"  Accelerated: {accel_time:.3f}s")
        
        # Verify results are close
        if np.allclose(result_baseline, result_accel, rtol=1e-5):
            print(f"  ✓ Results match (speedup: {baseline_time/accel_time:.2f}x)")
        else:
            print("  ✗ Results differ!")


def demonstrate_data_aggregation():
    """Demonstrate data aggregation acceleration."""
    print("\n=== Data Aggregation Demo ===")
    
    # Get universal accelerator
    accelerator = get_universal_accelerator()
    
    # Create test datasets
    sizes = [10000, 100000, 500000]
    
    for size in sizes:
        print(f"\nDataset size: {size:,} rows")
        
        # Create test DataFrame
        df = pd.DataFrame({
            'category': np.random.choice(['A', 'B', 'C', 'D', 'E'], size),
            'subcategory': np.random.choice(['X', 'Y', 'Z'], size),
            'value1': np.random.randn(size),
            'value2': np.random.randn(size) * 100,
            'value3': np.random.randn(size) * 1000
        })
        
        # Define aggregation
        group_by = ['category', 'subcategory']
        agg_func = {
            'value1': ['mean', 'std'],
            'value2': ['sum', 'min', 'max'],
            'value3': 'count'
        }
        
        # Baseline: pandas aggregation
        start = time.time()
        result_baseline = df.groupby(group_by).agg(agg_func)
        baseline_time = time.time() - start
        print(f"  Baseline (pandas): {baseline_time:.3f}s")
        
        # Accelerated aggregation
        start = time.time()
        result_accel = accelerator.accelerate(
            'aggregation',
            WorkloadType.DATA_AGGREGATION,
            df,
            group_by=group_by,
            agg_func=agg_func
        )
        accel_time = time.time() - start
        print(f"  Accelerated: {accel_time:.3f}s")
        
        # Compare shapes (full comparison would be too verbose)
        if result_baseline.shape == result_accel.shape:
            print(f"  ✓ Results match (speedup: {baseline_time/accel_time:.2f}x)")
        else:
            print("  ✗ Results differ!")


def demonstrate_platform_info():
    """Show platform and acceleration information."""
    print("\n=== Platform Information ===")
    
    # Get universal accelerator
    accelerator = get_universal_accelerator()
    
    # Get acceleration info
    info = accelerator.get_acceleration_info()
    
    print(f"\nPlatform: {info['platform']} / {info['machine']}")
    
    print(f"\nHardware Acceleration:")
    print(f"  Available: {info['hardware_acceleration']['available']}")
    print(f"  Type: {info['hardware_acceleration']['type']}")
    if info['hardware_acceleration']['features']:
        print(f"  Features: {', '.join(info['hardware_acceleration']['features'])}")
    
    print(f"\nFallback Acceleration:")
    print(f"  Available: {info['fallback_acceleration']['available']}")
    if info['fallback_acceleration']['optimizations']:
        print(f"  Optimizations: {', '.join(info['fallback_acceleration']['optimizations'])}")


def demonstrate_workload_benchmark():
    """Benchmark different workload types."""
    print("\n=== Workload Benchmarks ===")
    
    # Get universal accelerator
    accelerator = get_universal_accelerator()
    
    workloads = [
        (WorkloadType.MATRIX_OPERATIONS, 10000),
        (WorkloadType.DATA_AGGREGATION, 50000),
        (WorkloadType.STATISTICAL_ANALYSIS, 25000),
    ]
    
    for workload_type, data_size in workloads:
        print(f"\n{workload_type.name} (size: {data_size:,}):")
        
        try:
            results = accelerator.benchmark(workload_type, data_size)
            print(f"  Baseline: {results['baseline']:.3f}s")
            print(f"  Accelerated: {results['accelerated']:.3f}s")
            print(f"  Speedup: {results['speedup']:.2f}x")
        except Exception as e:
            print(f"  Benchmark failed: {e}")


def demonstrate_memory_optimization():
    """Demonstrate memory-optimized processing."""
    print("\n=== Memory Optimization Demo ===")
    
    # Get universal accelerator
    accelerator = get_universal_accelerator()
    
    # Create large dataset that would benefit from chunking
    print("\nCreating large dataset...")
    size = 1000000
    df = pd.DataFrame({
        'id': range(size),
        'value': np.random.randn(size),
        'category': np.random.choice(['A', 'B', 'C', 'D'], size)
    })
    
    print(f"Dataset size: {df.memory_usage().sum() / 1024**2:.1f} MB")
    
    # Process with acceleration (will use chunking if needed)
    print("\nProcessing with automatic memory optimization...")
    start = time.time()
    
    # Sort operation (memory intensive)
    result = accelerator.accelerate(
        'sorting',
        WorkloadType.DATA_PREPROCESSING,
        df,
        by=['category', 'value'],
        ascending=[True, False]
    )
    
    elapsed = time.time() - start
    print(f"Completed in {elapsed:.2f}s")
    print(f"Result shape: {result.shape}")


def main():
    """Run all demonstrations."""
    print("=== Universal Hardware Acceleration Demo ===")
    print(f"Platform: {platform.system()} {platform.machine()}")
    print(f"Python: {platform.python_version()}")
    
    # Show platform information first
    demonstrate_platform_info()
    
    # Run demonstrations
    demonstrate_matrix_operations()
    demonstrate_data_aggregation()
    demonstrate_workload_benchmark()
    demonstrate_memory_optimization()
    
    print("\n✓ Demo completed!")
    print("\nKey Benefits:")
    print("- Automatic platform detection")
    print("- Seamless fallback to optimized software")
    print("- Consistent API across all platforms")
    print("- Performance optimizations for each platform")


if __name__ == "__main__":
    main()