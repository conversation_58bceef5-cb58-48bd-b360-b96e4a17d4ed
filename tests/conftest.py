"""Root test configuration."""

import sys
from pathlib import Path
import pytest

# Add src to Python path
project_root = Path(__file__).parent.parent
src_path = project_root / 'src'
if str(src_path) not in sys.path:
    sys.path.insert(0, str(src_path))

# Apply numpy compatibility patch before any imports
try:
    # Simple inline patch to avoid circular imports
    import numpy as np
    if not hasattr(np, '__version__'):
        try:
            from importlib.metadata import version
            np.__version__ = version('numpy')
        except:
            np.__version__ = '1.24.0'  # Fallback version
except ImportError:
    pass

# Configure pytest
pytest_plugins = []

# Test markers
def pytest_configure(config):
    """Register custom markers."""
    config.addinivalue_line("markers", "unit: Unit tests")
    config.addinivalue_line("markers", "integration: Integration tests")
    config.addinivalue_line("markers", "performance: Performance tests")
    config.addinivalue_line("markers", "e2e: End-to-end tests")
    config.addinivalue_line("markers", "slow: Slow running tests")
    config.addinivalue_line("markers", "hardware: Hardware acceleration tests")

# Common fixtures
@pytest.fixture
def sample_data_path():
    """Path to sample test data."""
    return Path(__file__).parent / 'fixtures' / 'data'

@pytest.fixture
def temp_output_dir(tmp_path):
    """Temporary output directory for tests."""
    output_dir = tmp_path / 'output'
    output_dir.mkdir(exist_ok=True)
    return output_dir
