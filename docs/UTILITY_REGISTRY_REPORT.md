# Utility Registry Report

This report was generated by scanning the utils directory.

Total modules scanned: 20

## Column Mapping

- **ensure_columns_exist** (column_mapping.py): 
- **find_equivalent_column** (column_mapping.py): 
- **map_dataframe_columns** (column_mapping.py): No description

## File Operations

- **check_file_exists** (file_operations.py): 
- **convert_dataframe_to_serializable_list** (file_operations.py): 
- **ensure_dir_exists** (file_operations.py): 
- **ensure_directory_exists** (file_operations.py): 
- **get_file_extension** (file_operations.py): 
- **get_file_modification_time** (file_operations.py): 
- **get_output_dir** (file_operations.py): 
- **get_output_file_path** (file_operations.py): No description
- **is_file_newer_than** (file_operations.py): No description
- **list_files** (file_operations.py): No description
- **list_subdirectories** (file_operations.py): No description
- **load_data_from_pickle** (file_operations.py): 
- **load_dataframe_from_csv** (file_operations.py): 
- **load_dataframe_from_json** (file_operations.py): 
- **load_dict_from_json** (file_operations.py): 
- **save_data_to_multiple_formats** (file_operations.py): No description
- **save_data_to_pickle** (file_operations.py): 
- **save_dataframe** (file_operations.py): No description
- **save_dataframe_to_csv** (file_operations.py): No description
- **save_dataframe_to_json** (file_operations.py): No description
- **save_dataframe_to_parquet** (file_operations.py): No description
- **save_dict_to_json** (file_operations.py): No description

## Memory Management

- **chunk_generator** (memory_management.py): 
- **get_memory_usage** (memory_management.py): 
- **get_memory_usage_stats** (memory_management.py): Get system memory usage statistics.
- **is_under_memory_pressure** (memory_management.py): Check if system is under memory pressure.
- **optimize_dataframe** (memory_management.py): No description
- **optimize_dataframe_dtypes** (memory_management.py): 
- **process_dataframe_in_chunks** (memory_management.py): No description
- **process_in_chunks_v2** (memory_management.py): No description
- **read_csv_optimized_v2** (memory_management.py): No description
- **read_parquet_optimized_v2** (memory_management.py): No description
- **safely_concatenate_dfs** (memory_management.py): Safely concatenate DataFrames, handling empty or None values.
- **sample_large_dataframe** (memory_management.py): Sample a large DataFrame for exploratory analysis.

## Config

- **clear_config_cache** (config.py): 
- **deep_merge** (config.py): 
- **get_chart_config** (config.py): 
- **get_config_dir** (config.py): 
- **get_config_for_environment** (config.py): No description
- **get_core_config** (config.py): 
- **get_data_dir** (config.py): 
- **get_environment** (config.py): 
- **get_logging_config** (config.py): 
- **get_output_dir** (config.py): 
- **get_pipeline_config** (config.py): 
- **get_pipeline_dependencies** (config.py): 
- **get_processed_data_dir** (config.py): 
- **get_product_config** (config.py): 
- **get_project_root** (config.py): 
- **get_raw_data_dir** (config.py): 
- **load_json_config** (config.py): 
- **load_json_config_with_fallback** (config.py): No description
- **merge_configs** (config.py): 

## Feature Flags

- **get_all_feature_flags** (feature_flags.py): Return all registered feature flags as a list.
- **get_feature_flag** (feature_flags.py): 
- **get_feature_flag_manager** (feature_flags.py): 
- **is_advanced_caching_enabled** (feature_flags.py): Check if advanced caching is enabled.
- **is_advanced_validation_enabled** (feature_flags.py): Check if advanced validation is enabled.
- **is_comprehensive_logging_enabled** (feature_flags.py): Check if comprehensive logging is enabled.
- **is_distributed_caching_enabled** (feature_flags.py): Check if distributed caching is enabled.
- **is_feature_enabled** (feature_flags.py): 
- **is_hardware_acceleration_enabled** (feature_flags.py): Check if hardware acceleration is enabled.
- **is_refactored_data_enabled** (feature_flags.py): Check if refactored data module is enabled.
- **is_refactored_models_enabled** (feature_flags.py): Check if refactored models are enabled.
- **is_refactored_pipelines_enabled** (feature_flags.py): Check if refactored pipelines are enabled.
- **is_refactored_visualization_enabled** (feature_flags.py): Check if refactored visualization is enabled.
- **register_default_feature_flags** (feature_flags.py): Register default feature flags.
- **register_feature_flag** (feature_flags.py): No description
- **set_feature_flag** (feature_flags.py): 
- **set_feature_state** (feature_flags.py): 
- **set_user_id** (feature_flags.py): 

## Standardization

- **ensure_standard_columns** (standardization.py): 
- **standardize_columns** (standardization.py): No description
- **standardize_data_types** (standardization.py): 

## Others

- **acquire_dataframe** (memory_pool.py): No description
- **allocate_memory** (memory_pool.py): Allocate memory from the pool.
- **auto_discover_utilities** (registry.py): 
- **create_empty_dependency_results** (pipeline_dependency_manager.py): Create empty dependency results for a pipeline with the expected structure.
- **create_view** (dataframe_views.py): No description
- **ensure_dependency_directories** (pipeline_dependency_manager.py): Ensure output directories exist for a pipeline and its dependencies.
- **ensure_dir_exists** (generate_world_exports_from_baci.py): 
- **extract_year_from_file** (generate_world_exports_from_baci.py): 
- **find_similar_functions** (registry.py): 
- **generate_world_exports** (generate_world_exports_from_baci.py): 
- **get_baci_files** (generate_world_exports_from_baci.py): 
- **get_execution_order** (pipeline_dependency_manager.py): Determine the execution order for a set of pipelines based on dependencies.
- **get_health_dashboard** (health_dashboard.py): Get the global health dashboard instance
- **get_memory_monitor** (memory_monitor.py): Get the global memory monitor instance.
- **get_memory_pool** (memory_pool.py): Get the global memory pool instance.
- **get_memory_pool** (memory_pool.py): Get the global memory pool instance.
- **get_memory_pool_initializer** (memory_pool_initializer.py): 
- **get_memory_profiler** (memory_profiler.py): Get or create the global memory profiler instance.
- **get_memory_profiler** (memory_monitor.py): Get the global memory profiler instance.
- **get_memory_report** (memory_monitor.py): Get a comprehensive memory report.
- **get_optimization_monitor** (optimization_monitor.py): Get the global optimization monitor instance
- **get_performance_collector** (performance_collector.py): Get the global performance collector instance
- **get_pipeline_dependencies** (pipeline_dependency_manager.py): Get the list of dependencies for a pipeline.
- **get_pool_stats** (memory_pool.py): Get statistics from the global pool.
- **get_progress_tracker** (progress_tracker.py): 
- **get_registry** (registry.py): Get the global utility registry instance.
- **initialize_memory_system** (memory_pool_initializer.py): 
- **log_to_file** (debug_helpers.py): Log data to a file for debugging
- **main** (generate_world_exports_from_baci.py): Main execution function.
- **merge_years_data** (generate_world_exports_from_baci.py): 
- **monitor_memory_usage** (memory_pool.py): Monitor current memory usage.
- **optimize_dataframe** (memory_profiler.py): Apply memory optimization to a DataFrame by downcasting numeric types.
- **optimize_dataframe_memory** (generate_world_exports_from_baci.py): 
- **optimize_memory_usage** (memory_pool.py): Optimize memory usage of a DataFrame.
- **process_baci_file** (generate_world_exports_from_baci.py): 
- **process_year** (generate_world_exports_from_baci.py): 
- **profile_memory** (memory_profiler.py): Decorator to profile memory usage before and after a function call.
- **profile_memory** (memory_monitor.py): 
- **register_function** (registry.py): No description
- **release_dataframe** (memory_pool.py): Release a DataFrame back to the global pool.
- **release_memory** (memory_pool.py): Release memory back to the pool.
- **start_memory_monitoring** (memory_monitor.py): Start global memory monitoring.
- **stop_memory_monitoring** (memory_monitor.py): Stop global memory monitoring.
- **timed_operation** (performance_collector.py): Decorator to automatically time function execution
- **transform_dependency_data** (pipeline_dependency_manager.py): No description
- **trigger_gc** (memory_pool.py): Manually trigger garbage collection.
- **validate_dependency_results** (pipeline_dependency_manager.py): No description
- **validate_no_duplicates** (registry.py): 
- **with_memory_pool** (memory_pool.py): 

## Potential Duplicates or Similar Functions

- **get_output_dir** appears in both file_operations.py and config.py
- **optimize_dataframe** appears in both memory_management.py and memory_profiler.py
- **get_pipeline_dependencies** appears in both config.py and pipeline_dependency_manager.py
- **get_memory_pool** appears in both memory_pool.py and memory_pool.py
- **ensure_dir_exists** appears in both file_operations.py and generate_world_exports_from_baci.py
- **get_memory_profiler** appears in both memory_profiler.py and memory_monitor.py
- **profile_memory** appears in both memory_profiler.py and memory_monitor.py

## Summary Statistics

- Total functions: 126
- Categories: 7
