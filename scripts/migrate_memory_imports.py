#!/usr/bin/env python3
"""<PERSON>ript to migrate memory utility imports to the consolidated memory_management module."""

import os
import re
from pathlib import Path

def update_imports_in_file(file_path):
    """Update memory utility imports in a single file."""
    
    # Skip archive and test files unless they're active tests
    if '/archive/' in str(file_path) or 'test_memory_utils.py' in str(file_path):
        return False
        
    try:
        with open(file_path, 'r') as f:
            content = f.read()
        
        original_content = content
        
        # Pattern to match imports from memory_optimizer or memory_utils
        patterns = [
            # Full module imports
            (r'from yemen_trade_diagnostic\.utils\.memory_optimizer import (.+)', 
             r'from yemen_trade_diagnostic.utils.memory_management import \1'),
            (r'from yemen_trade_diagnostic\.utils\.memory_utils import (.+)', 
             r'from yemen_trade_diagnostic.utils.memory_management import \1'),
            # Aliased imports
            (r'from yemen_trade_diagnostic\.utils import memory_optimizer\b',
             r'from yemen_trade_diagnostic.utils import memory_management as memory_optimizer'),
            (r'from yemen_trade_diagnostic\.utils import memory_utils\b',
             r'from yemen_trade_diagnostic.utils import memory_management as memory_utils'),
            # Direct module imports
            (r'import yemen_trade_diagnostic\.utils\.memory_optimizer\b',
             r'import yemen_trade_diagnostic.utils.memory_management as memory_optimizer'),
            (r'import yemen_trade_diagnostic\.utils\.memory_utils\b',
             r'import yemen_trade_diagnostic.utils.memory_management as memory_utils'),
        ]
        
        for pattern, replacement in patterns:
            content = re.sub(pattern, replacement, content)
        
        # If content changed, write it back
        if content != original_content:
            with open(file_path, 'w') as f:
                f.write(content)
            print(f"✅ Updated: {file_path}")
            return True
        
        return False
        
    except Exception as e:
        print(f"❌ Error updating {file_path}: {e}")
        return False

def main():
    """Main function to migrate all memory imports."""
    
    src_root = Path(__file__).parent.parent / 'src'
    tests_root = Path(__file__).parent.parent / 'tests'
    
    updated_files = []
    
    # Search for Python files in src and tests
    for root_dir in [src_root, tests_root]:
        for py_file in root_dir.rglob('*.py'):
            if update_imports_in_file(py_file):
                updated_files.append(py_file)
    
    print(f"\n✅ Migration complete! Updated {len(updated_files)} files")
    
    if updated_files:
        print("\nFiles updated:")
        for f in updated_files:
            print(f"  - {f}")

if __name__ == "__main__":
    main()