#!/bin/bash
"""
Fix test environment issues including numpy compatibility.
"""

echo "Yemen Trade Diagnostic - Test Environment Fix"
echo "============================================="

# Get the directory of this script
SCRIPT_DIR="$( cd "$( dirname "${BASH_SOURCE[0]}" )" && pwd )"
PROJECT_ROOT="$( cd "$SCRIPT_DIR/../.." && pwd )"

echo "Project root: $PROJECT_ROOT"

# Check if venv exists
if [ ! -d "$PROJECT_ROOT/venv_3.9" ]; then
    echo "❌ Error: Virtual environment not found at $PROJECT_ROOT/venv_3.9"
    echo "Creating new virtual environment..."
    python3.9 -m venv "$PROJECT_ROOT/venv_3.9"
fi

# Activate virtual environment
echo "Activating virtual environment..."
source "$PROJECT_ROOT/venv_3.9/bin/activate"

# Check Python version
echo "Python version: $(python --version)"

# Reinstall pip
echo "Reinstalling pip..."
curl -sSL https://bootstrap.pypa.io/get-pip.py | python

# Install compatible versions
echo "Installing compatible numpy and pandas versions..."
python -m pip install --upgrade pip setuptools wheel
python -m pip install numpy==1.24.3 pandas==2.0.3 --force-reinstall

# Install other requirements
echo "Installing project requirements..."
python -m pip install -r "$PROJECT_ROOT/requirements.txt" --upgrade

# Test imports
echo ""
echo "Testing imports..."
python -c "
import numpy as np
print(f'✓ NumPy {np.__version__} imported successfully')
import pandas as pd  
print(f'✓ Pandas {pd.__version__} imported successfully')
"

echo ""
echo "✅ Test environment fixed!"
echo ""
echo "To run tests, use:"
echo "  source venv_3.9/bin/activate"
echo "  python scripts/testing/run_all_tests.py"