#!/usr/bin/env python3
"""Test numpy compatibility fix."""

import sys
from pathlib import Path

# Add project root to path
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root / "src"))

# Import our compatibility module first
from yemen_trade_diagnostic.utils.numpy_compat import ensure_numpy_compatibility
ensure_numpy_compatibility()

# Now try to import pandas and numpy
try:
    import numpy as np
    print(f"✓ NumPy imported successfully")
    print(f"  Version: {np.__version__}")
    
    import pandas as pd
    print(f"✓ Pandas imported successfully")
    print(f"  Version: {pd.__version__}")
    
    # Try to create a simple DataFrame
    df = pd.DataFrame({'a': [1, 2, 3], 'b': [4, 5, 6]})
    print(f"✓ DataFrame created successfully")
    print(f"  Shape: {df.shape}")
    
    # Try some numpy operations
    arr = np.array([1, 2, 3])
    print(f"✓ NumPy array created successfully")
    print(f"  Array: {arr}")
    
    print("\n✅ All compatibility tests passed!")
    
except Exception as e:
    print(f"\n❌ Error: {e}")
    import traceback
    traceback.print_exc()