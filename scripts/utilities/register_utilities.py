#!/usr/bin/env python3
"""
Register all utility functions in the central registry.

This script scans the utils module and registers all functions
to prevent duplication and ensure consistent usage.
"""

import sys
from pathlib import Path

# Add project root to path
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root / "src"))

from yemen_trade_diagnostic.utils.registry import (
    get_registry, register_function, auto_discover_utilities,
    validate_no_duplicates, find_similar_functions
)
from yemen_trade_diagnostic.utils import (
    column_mapping, config, memory_management, file_operations,
    standardization, feature_flags, performance_collector
)


def manual_registration():
    """Manually register key utility functions with proper metadata."""
    registry = get_registry()
    
    # Column mapping functions
    registry.register(
        function=column_mapping.map_dataframe_columns,
        category="Column Mapping",
        description="Map DataFrame columns using standardized naming"
    )
    
    registry.register(
        function=column_mapping.find_equivalent_column,
        category="Column Mapping", 
        description="Find equivalent column name in DataFrame"
    )
    
    registry.register(
        function=column_mapping.ensure_columns_exist,
        category="Column Mapping",
        description="Ensure required columns exist in DataFrame"
    )
    
    # Memory management functions
    registry.register(
        function=memory_management.optimize_dataframe,
        category="Memory Management",
        description="Optimize DataFrame memory usage by downcasting types"
    )
    
    registry.register(
        function=memory_management.process_dataframe_in_chunks,
        category="Memory Management",
        description="Process large DataFrame in memory-efficient chunks"
    )
    
    registry.register(
        function=memory_management.estimate_dataframe_memory,
        category="Memory Management",
        description="Estimate memory usage of a DataFrame"
    )
    
    registry.register(
        function=memory_management.read_csv_optimized_v2,
        category="Memory Management",
        description="Read CSV with advanced memory optimization",
        aliases=["read_csv_optimized"]
    )
    
    # File operations
    registry.register(
        function=file_operations.ensure_dir_exists,
        category="File Operations",
        description="Ensure directory exists, create if needed"
    )
    
    registry.register(
        function=file_operations.save_dataframe,
        category="File Operations",
        description="Save DataFrame to various formats"
    )
    
    registry.register(
        function=file_operations.save_dataframe_to_csv,
        category="File Operations",
        description="Save DataFrame to CSV file"
    )
    
    registry.register(
        function=file_operations.load_dataframe_from_csv,
        category="File Operations",
        description="Load DataFrame from CSV file"
    )
    
    # Configuration functions
    registry.register(
        function=config.get_config,
        category="Configuration",
        description="Get configuration value by key"
    )
    
    registry.register(
        function=config.get_env,
        category="Configuration",
        description="Get environment variable with fallback"
    )
    
    # Feature flags
    registry.register(
        function=feature_flags.is_feature_enabled,
        category="Feature Flags",
        description="Check if a feature flag is enabled"
    )
    
    registry.register(
        function=feature_flags.get_feature_config,
        category="Feature Flags",
        description="Get feature configuration"
    )
    
    # Standardization
    registry.register(
        function=standardization.standardize_country_name,
        category="Data Standardization",
        description="Standardize country names to ISO format"
    )
    
    registry.register(
        function=standardization.standardize_product_code,
        category="Data Standardization",
        description="Standardize product codes to HS format"
    )
    
    # Performance collection
    registry.register(
        function=performance_collector.collect_performance_metrics,
        category="Performance Monitoring",
        description="Collect system performance metrics"
    )


def main():
    """Main function to register utilities and generate report."""
    print("Yemen Trade Diagnostic - Utility Registration")
    print("=" * 50)
    
    # Manual registration of key functions
    print("\n1. Registering key utility functions...")
    manual_registration()
    
    # Auto-discover remaining functions
    print("\n2. Auto-discovering utility functions...")
    auto_discover_utilities()
    
    # Get registry and stats
    registry = get_registry()
    stats = registry.get_stats()
    
    print(f"\n3. Registration complete!")
    print(f"   - Total functions: {stats['total_functions']}")
    print(f"   - Categories: {stats['categories']}")
    print(f"   - Modules: {stats['modules']}")
    print(f"   - Aliases: {stats['aliases']}")
    
    # Check for duplicates
    print("\n4. Checking for duplicates...")
    if validate_no_duplicates():
        print("   ✓ No duplicate functions found")
    else:
        print("   ✗ Duplicates detected (see logs)")
        
    # Find similar functions
    print("\n5. Finding similar functions...")
    similar = find_similar_functions(threshold=0.7)
    if similar:
        print(f"   Found {len(similar)} potentially similar functions:")
        for func1, func2, similarity in similar[:5]:  # Show top 5
            print(f"   - {func1} ≈ {func2} ({similarity:.0%})")
    else:
        print("   ✓ No suspiciously similar functions found")
        
    # Generate report
    print("\n6. Generating registry report...")
    report = registry.generate_report()
    
    # Save report
    report_path = project_root / "docs" / "UTILITY_REGISTRY_REPORT.md"
    report_path.write_text(report)
    print(f"   Report saved to: {report_path}")
    
    # Show categories
    print("\n7. Registered Categories:")
    for category in sorted(registry.get_categories()):
        count = len(registry.get_by_category(category))
        print(f"   - {category}: {count} functions")
        
    print("\n✓ Utility registration complete!")


if __name__ == "__main__":
    main()