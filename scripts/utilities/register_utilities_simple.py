#!/usr/bin/env python3
"""
Simple utility registration script that avoids import issues.
"""

import sys
from pathlib import Path

# Add project root to path
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root / "src"))


def main():
    """Generate utility registry report by scanning the utils directory."""
    import os
    import importlib
    import inspect
    
    utils_path = project_root / "src" / "yemen_trade_diagnostic" / "utils"
    
    # Dictionary to store function information
    registry = {
        "column_mapping": [],
        "file_operations": [],
        "memory_management": [],
        "config": [],
        "feature_flags": [],
        "standardization": [],
        "others": []
    }
    
    # Scan Python files in utils directory
    for filename in os.listdir(utils_path):
        if filename.endswith('.py') and not filename.startswith('_'):
            module_name = filename[:-3]  # Remove .py extension
            
            try:
                # Read file to get function names
                file_path = utils_path / filename
                with open(file_path, 'r') as f:
                    content = f.read()
                
                # Extract function definitions
                functions = []
                lines = content.split('\n')
                for i, line in enumerate(lines):
                    if line.startswith('def ') and not line.startswith('def _'):
                        # Extract function name
                        func_name = line.split('(')[0].replace('def ', '')
                        
                        # Try to get docstring (first line)
                        docstring = "No description"
                        if i + 1 < len(lines) and '"""' in lines[i + 1]:
                            docstring_start = i + 1
                            for j in range(docstring_start, min(docstring_start + 10, len(lines))):
                                if '"""' in lines[j]:
                                    if j == docstring_start:
                                        # Single line docstring
                                        docstring = lines[j].strip().replace('"""', '')
                                    else:
                                        # Multi-line, take first line
                                        docstring = lines[docstring_start + 1].strip()
                                    break
                        
                        functions.append({
                            'name': func_name,
                            'module': module_name,
                            'description': docstring
                        })
                
                # Categorize functions
                category = "others"
                if module_name in registry:
                    category = module_name
                
                registry[category].extend(functions)
                
            except Exception as e:
                print(f"Error processing {filename}: {e}")
    
    # Generate report
    report_lines = [
        "# Utility Registry Report",
        "",
        "This report was generated by scanning the utils directory.",
        "",
        f"Total modules scanned: {len([f for f in os.listdir(utils_path) if f.endswith('.py') and not f.startswith('_')])}",
        ""
    ]
    
    # Add functions by category
    for category, functions in registry.items():
        if functions:
            report_lines.append(f"## {category.replace('_', ' ').title()}")
            report_lines.append("")
            
            for func in sorted(functions, key=lambda f: f['name']):
                report_lines.append(f"- **{func['name']}** ({func['module']}.py): {func['description']}")
            
            report_lines.append("")
    
    # Check for potential duplicates
    all_functions = []
    for funcs in registry.values():
        all_functions.extend(funcs)
    
    # Find functions with similar names
    report_lines.append("## Potential Duplicates or Similar Functions")
    report_lines.append("")
    
    seen_names = {}
    duplicates_found = False
    
    for func in all_functions:
        name = func['name'].lower()
        if name in seen_names:
            if not duplicates_found:
                duplicates_found = True
            report_lines.append(f"- **{func['name']}** appears in both {seen_names[name]['module']}.py and {func['module']}.py")
        else:
            seen_names[name] = func
    
    if not duplicates_found:
        report_lines.append("No exact duplicates found.")
    
    report_lines.append("")
    
    # Summary statistics
    total_functions = len(all_functions)
    report_lines.extend([
        "## Summary Statistics",
        "",
        f"- Total functions: {total_functions}",
        f"- Categories: {len([c for c, f in registry.items() if f])}",
        ""
    ])
    
    # Save report
    report_path = project_root / "docs" / "UTILITY_REGISTRY_REPORT.md"
    report_path.write_text("\n".join(report_lines))
    
    print("Yemen Trade Diagnostic - Utility Registry Report")
    print("=" * 50)
    print(f"\nReport generated successfully!")
    print(f"Total functions found: {total_functions}")
    print(f"Report saved to: {report_path}")
    
    # Show summary by category
    print("\nFunctions by category:")
    for category, functions in registry.items():
        if functions:
            print(f"  - {category}: {len(functions)} functions")


if __name__ == "__main__":
    main()