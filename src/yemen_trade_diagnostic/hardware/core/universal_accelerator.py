"""
Universal Hardware Accelerator with Comprehensive Fallback Support

This module provides a universal hardware acceleration interface that works
seamlessly across all platforms, with automatic fallback to optimized software
implementations when hardware acceleration is not available.
"""
# Standard library imports
import logging
import platform
from typing import Any, Dict, Optional, Union

# Third-party imports
import numpy as np
import pandas as pd

# Project imports
from yemen_trade_diagnostic.errors import protect, error_context, OperationType
from yemen_trade_diagnostic.interfaces.hardware_interface import (
    AccelerationType,
    WorkloadType,
    HardwareCapabilities
)
from yemen_trade_diagnostic.hardware.core.detector import get_hardware_detector
from yemen_trade_diagnostic.hardware.core.fallback import create_fallback_accelerator

logger = logging.getLogger(__name__)

# Singleton instance
_universal_accelerator_instance = None


def get_universal_accelerator() -> 'UniversalHardwareAccelerator':
    """
    Get the singleton instance of the universal hardware accelerator.
    
    Returns:
        UniversalHardwareAccelerator instance
    """
    global _universal_accelerator_instance
    if _universal_accelerator_instance is None:
        _universal_accelerator_instance = UniversalHardwareAccelerator()
    return _universal_accelerator_instance


class UniversalHardwareAccelerator:
    """
    Universal hardware accelerator that provides seamless acceleration
    across all platforms with automatic fallback support.
    
    This class automatically detects available hardware acceleration
    capabilities and falls back to optimized software implementations
    when hardware acceleration is not available.
    """
    
    def __init__(self):
        """Initialize the universal hardware accelerator."""
        self.platform = platform.system()
        self.machine = platform.machine()
        
        # Detect hardware capabilities
        self.detector = get_hardware_detector()
        self.capabilities = self._detect_capabilities()
        
        # Initialize acceleration backends
        self.hardware_accelerator = None
        self.fallback_accelerator = None
        
        # Setup acceleration backends
        self._setup_accelerators()
        
        # Log initialization
        logger.info(
            f"Universal accelerator initialized for {self.platform}/{self.machine}"
        )
        logger.info(
            f"Hardware acceleration: {self.has_hardware_acceleration}, "
            f"Fallback: {self.has_fallback_acceleration}"
        )
    
    @protect("detect_capabilities", OperationType.HARDWARE_ACCELERATION)
    def _detect_capabilities(self) -> Dict[str, Any]:
        """Detect all available acceleration capabilities."""
        try:
            # Get hardware capabilities
            hw_caps = self.detector.get_acceleration_capabilities()
            
            # Determine overall capabilities
            return {
                'platform': self.platform,
                'machine': self.machine,
                'apple_silicon': self.detector.is_apple_silicon,
                'has_metal': hw_caps.get('metal', False),
                'has_neural_engine': hw_caps.get('neural_engine', False),
                'cpu_acceleration': True,  # Always available
                'hardware_caps': hw_caps
            }
        except Exception as e:
            logger.warning(f"Failed to detect hardware capabilities: {e}")
            return {
                'platform': self.platform,
                'machine': self.machine,
                'apple_silicon': False,
                'has_metal': False,
                'has_neural_engine': False,
                'cpu_acceleration': True,
                'hardware_caps': {}
            }
    
    @protect("setup_accelerators", OperationType.HARDWARE_ACCELERATION)
    def _setup_accelerators(self):
        """Setup hardware and fallback accelerators."""
        # Try to initialize hardware accelerator if on Apple Silicon
        if self.capabilities.get('apple_silicon', False):
            try:
                from yemen_trade_diagnostic.hardware.core.accelerator import (
                    get_hardware_accelerator
                )
                self.hardware_accelerator = get_hardware_accelerator()
                logger.info("Hardware accelerator initialized successfully")
            except Exception as e:
                logger.warning(f"Failed to initialize hardware accelerator: {e}")
                self.hardware_accelerator = None
        
        # Always initialize fallback accelerator
        try:
            self.fallback_accelerator = create_fallback_accelerator()
            logger.info("Fallback accelerator initialized successfully")
        except Exception as e:
            logger.error(f"Failed to initialize fallback accelerator: {e}")
            # Create minimal fallback
            self.fallback_accelerator = None
    
    @property
    def has_hardware_acceleration(self) -> bool:
        """Check if hardware acceleration is available."""
        return self.hardware_accelerator is not None
    
    @property
    def has_fallback_acceleration(self) -> bool:
        """Check if fallback acceleration is available."""
        return self.fallback_accelerator is not None
    
    @protect("accelerate_operation", OperationType.COMPUTATION)
    def accelerate(
        self,
        operation: str,
        workload_type: WorkloadType,
        data: Union[pd.DataFrame, np.ndarray],
        **kwargs
    ) -> Any:
        """
        Accelerate an operation using the best available method.
        
        This method automatically selects between hardware acceleration
        and optimized software fallbacks based on availability and
        workload characteristics.
        
        Args:
            operation: Name of the operation to accelerate
            workload_type: Type of workload
            data: Data to process
            **kwargs: Additional operation-specific arguments
            
        Returns:
            Processed result
        """
        # Log operation request
        data_shape = data.shape if hasattr(data, 'shape') else len(data)
        logger.debug(
            f"Accelerating {operation} ({workload_type}) on data shape {data_shape}"
        )
        
        # Try hardware acceleration first if available and suitable
        if self.has_hardware_acceleration and self._should_use_hardware(
            workload_type, data
        ):
            try:
                with error_context(
                    "hardware_acceleration", OperationType.HARDWARE_ACCELERATION
                ):
                    result = self._hardware_accelerate(
                        operation, workload_type, data, **kwargs
                    )
                    logger.debug(f"Hardware acceleration successful for {operation}")
                    return result
            except Exception as e:
                logger.warning(
                    f"Hardware acceleration failed for {operation}: {e}, "
                    "falling back to software"
                )
        
        # Use fallback acceleration
        if self.has_fallback_acceleration:
            try:
                with error_context("fallback_acceleration", OperationType.COMPUTATION):
                    result = self.fallback_accelerator.accelerate(
                        operation, workload_type, data, **kwargs
                    )
                    logger.debug(f"Fallback acceleration successful for {operation}")
                    return result
            except Exception as e:
                logger.warning(f"Fallback acceleration failed for {operation}: {e}")
        
        # Last resort: return unprocessed data
        logger.warning(
            f"No acceleration available for {operation}, returning original data"
        )
        return data
    
    def _should_use_hardware(
        self, workload_type: WorkloadType, data: Union[pd.DataFrame, np.ndarray]
    ) -> bool:
        """
        Determine if hardware acceleration should be used.
        
        Hardware acceleration might not always be optimal, especially
        for small datasets where the overhead outweighs the benefits.
        """
        # Get data size
        if isinstance(data, pd.DataFrame):
            data_size = len(data) * len(data.columns)
        elif isinstance(data, np.ndarray):
            data_size = data.size
        else:
            data_size = len(data) if hasattr(data, '__len__') else 0
        
        # Hardware acceleration threshold varies by workload
        thresholds = {
            WorkloadType.MATRIX_OPERATIONS: 10000,
            WorkloadType.DATA_AGGREGATION: 50000,
            WorkloadType.STATISTICAL_ANALYSIS: 25000,
            WorkloadType.MACHINE_LEARNING: 10000,
            WorkloadType.TIME_SERIES: 50000,
            WorkloadType.PARALLEL_COMPUTATION: 5000,
        }
        
        threshold = thresholds.get(workload_type, 25000)
        return data_size >= threshold
    
    def _hardware_accelerate(
        self,
        operation: str,
        workload_type: WorkloadType,
        data: Union[pd.DataFrame, np.ndarray],
        **kwargs
    ) -> Any:
        """Execute operation using hardware acceleration."""
        # Map operations to hardware accelerator methods
        operation_map = {
            'matrix_multiply': self._hw_matrix_multiply,
            'aggregation': self._hw_aggregation,
            'sorting': self._hw_sorting,
            'filtering': self._hw_filtering,
            'statistical_analysis': self._hw_statistical,
            'time_series_analysis': self._hw_time_series,
        }
        
        handler = operation_map.get(operation)
        if handler:
            return handler(data, **kwargs)
        else:
            # Generic hardware acceleration
            return self.hardware_accelerator.process(data, **kwargs)
    
    def _hw_matrix_multiply(self, a: np.ndarray, b: np.ndarray, **kwargs) -> np.ndarray:
        """Hardware-accelerated matrix multiplication."""
        # Use Metal Performance Shaders if available
        if hasattr(self.hardware_accelerator, 'metal_matrix_multiply'):
            return self.hardware_accelerator.metal_matrix_multiply(a, b, **kwargs)
        else:
            return a @ b
    
    def _hw_aggregation(
        self, df: pd.DataFrame, group_by: list, agg_func: dict, **kwargs
    ) -> pd.DataFrame:
        """Hardware-accelerated aggregation."""
        # Use optimized aggregation if available
        if hasattr(self.hardware_accelerator, 'accelerate_aggregation'):
            return self.hardware_accelerator.accelerate_aggregation(
                df, group_by, agg_func, **kwargs
            )
        else:
            return df.groupby(group_by).agg(agg_func)
    
    def _hw_sorting(self, data: Union[pd.DataFrame, np.ndarray], **kwargs) -> Any:
        """Hardware-accelerated sorting."""
        if hasattr(self.hardware_accelerator, 'accelerate_sort'):
            return self.hardware_accelerator.accelerate_sort(data, **kwargs)
        else:
            if isinstance(data, pd.DataFrame):
                return data.sort_values(**kwargs)
            else:
                return np.sort(data, **kwargs)
    
    def _hw_filtering(self, data: pd.DataFrame, condition: Any, **kwargs) -> pd.DataFrame:
        """Hardware-accelerated filtering."""
        if hasattr(self.hardware_accelerator, 'accelerate_filter'):
            return self.hardware_accelerator.accelerate_filter(data, condition, **kwargs)
        else:
            return data[condition]
    
    def _hw_statistical(self, data: Union[pd.DataFrame, np.ndarray], **kwargs) -> Dict:
        """Hardware-accelerated statistical analysis."""
        if hasattr(self.hardware_accelerator, 'accelerate_statistics'):
            return self.hardware_accelerator.accelerate_statistics(data, **kwargs)
        else:
            # Basic statistics
            if isinstance(data, pd.DataFrame):
                return data.describe().to_dict()
            else:
                return {
                    'mean': np.mean(data),
                    'std': np.std(data),
                    'min': np.min(data),
                    'max': np.max(data)
                }
    
    def _hw_time_series(self, data: pd.DataFrame, **kwargs) -> pd.DataFrame:
        """Hardware-accelerated time series analysis."""
        if hasattr(self.hardware_accelerator, 'accelerate_time_series'):
            return self.hardware_accelerator.accelerate_time_series(data, **kwargs)
        else:
            # Basic time series operations
            return data.resample(**kwargs).mean() if 'rule' in kwargs else data
    
    def get_acceleration_info(self) -> Dict[str, Any]:
        """
        Get information about available acceleration methods.
        
        Returns:
            Dictionary with acceleration capabilities and status
        """
        info = {
            'platform': self.platform,
            'machine': self.machine,
            'hardware_acceleration': {
                'available': self.has_hardware_acceleration,
                'type': 'Apple Silicon' if self.capabilities.get('apple_silicon') else 'None',
                'features': []
            },
            'fallback_acceleration': {
                'available': self.has_fallback_acceleration,
                'optimizations': []
            }
        }
        
        # Add hardware features
        if self.has_hardware_acceleration:
            if self.capabilities.get('has_metal'):
                info['hardware_acceleration']['features'].append('Metal GPU')
            if self.capabilities.get('has_neural_engine'):
                info['hardware_acceleration']['features'].append('Neural Engine')
        
        # Add fallback optimizations
        if self.has_fallback_acceleration:
            fb = self.fallback_accelerator.fallback_manager
            if fb.has_mkl:
                info['fallback_acceleration']['optimizations'].append('Intel MKL')
            if fb.has_openblas:
                info['fallback_acceleration']['optimizations'].append('OpenBLAS')
            if fb.has_numba:
                info['fallback_acceleration']['optimizations'].append('Numba JIT')
            
            # Platform-specific optimizations
            if fb.is_x86:
                info['fallback_acceleration']['optimizations'].append('x86 SIMD')
            elif fb.is_arm:
                info['fallback_acceleration']['optimizations'].append('ARM NEON')
        
        return info
    
    def benchmark(self, workload_type: WorkloadType, data_size: int = 10000) -> Dict[str, float]:
        """
        Benchmark acceleration performance for a given workload.
        
        Args:
            workload_type: Type of workload to benchmark
            data_size: Size of test data
            
        Returns:
            Dictionary with benchmark results (times in seconds)
        """
        import time
        
        # Create test data
        if workload_type == WorkloadType.MATRIX_OPERATIONS:
            test_data = np.random.randn(int(np.sqrt(data_size)), int(np.sqrt(data_size)))
            operation = 'matrix_multiply'
            kwargs = {'b': test_data.copy()}
        else:
            test_data = pd.DataFrame({
                'x': np.random.randn(data_size),
                'y': np.random.randn(data_size),
                'group': np.random.choice(['A', 'B', 'C'], data_size)
            })
            operation = 'aggregation'
            kwargs = {'group_by': ['group'], 'agg_func': {'x': 'mean', 'y': 'sum'}}
        
        results = {}
        
        # Benchmark baseline (no acceleration)
        start = time.time()
        if operation == 'matrix_multiply':
            _ = test_data @ kwargs['b']
        else:
            _ = test_data.groupby(kwargs['group_by']).agg(kwargs['agg_func'])
        results['baseline'] = time.time() - start
        
        # Benchmark with acceleration
        start = time.time()
        _ = self.accelerate(operation, workload_type, test_data, **kwargs)
        results['accelerated'] = time.time() - start
        
        # Calculate speedup
        results['speedup'] = results['baseline'] / results['accelerated']
        
        return results