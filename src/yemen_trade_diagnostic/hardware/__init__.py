"""
Hardware Module

This module supports hardware acceleration for improved performance on Apple Silicon,
particularly M3 Pro processors. It leverages the following optimizations:

- Memory optimization for efficient data handling
- Parallel processing for multi-core utilization
- Neural Engine acceleration for matrix operations
- Metal GPU acceleration for visualization

The module provides a clean, unified interface for hardware detection and acceleration.
"""

# Standard library imports
from typing import Optional

# Import core components using lazy imports to avoid circular dependencies
_hardware_detector = None
_hardware_accelerator = None
_universal_accelerator = None


def get_hardware_detector():
    """
    Get the singleton instance of the hardware detector.

    Returns:
        HardwareDetector: The hardware detector instance
    """
    global _hardware_detector
    if _hardware_detector is None:
        # Project imports
        from yemen_trade_diagnostic.hardware.core.detector import HardwareDetector
        _hardware_detector = HardwareDetector()
    return _hardware_detector


def get_hardware_accelerator():
    """
    Get the singleton instance of the hardware accelerator.

    Returns:
        HardwareAccelerator: The hardware accelerator instance
    """
    global _hardware_accelerator
    if _hardware_accelerator is None:
        # Project imports
        from yemen_trade_diagnostic.hardware.core.accelerator import HardwareAccelerator
        _hardware_accelerator = HardwareAccelerator()
    return _hardware_accelerator


def get_universal_accelerator():
    """
    Get the singleton instance of the universal hardware accelerator.
    
    This accelerator works on all platforms with automatic fallback to
    optimized software implementations when hardware acceleration is
    not available.

    Returns:
        UniversalHardwareAccelerator: The universal accelerator instance
    """
    global _universal_accelerator
    if _universal_accelerator is None:
        # Project imports
        from yemen_trade_diagnostic.hardware.core.universal_accelerator import UniversalHardwareAccelerator
        _universal_accelerator = UniversalHardwareAccelerator()
    return _universal_accelerator


# Convenience functions
def is_m3_pro():
    """Check if the system has an M3 Pro processor."""
    return get_hardware_detector().is_m3_pro


def is_apple_silicon():
    """Check if the system has Apple Silicon."""
    return get_hardware_detector().is_apple_silicon


# Public API
__all__ = [
    'get_hardware_detector',
    'get_hardware_accelerator',
    'is_m3_pro',
    'is_apple_silicon',
]
