"""
Utility Registry System

This module provides a central registry for all utility functions to prevent
code duplication and ensure consistent usage across the codebase.
"""

import inspect
import logging
from collections import defaultdict
from dataclasses import dataclass, field
from pathlib import Path
from typing import Any, Callable, Dict, List, Optional, Set, Tuple

from yemen_trade_diagnostic.errors import protect, OperationType

logger = logging.getLogger(__name__)


@dataclass
class FunctionInfo:
    """Information about a registered function."""
    
    name: str
    module: str
    function: Callable
    category: str
    description: str
    parameters: List[str]
    return_type: Optional[str] = None
    aliases: List[str] = field(default_factory=list)
    deprecated: bool = False
    replacement: Optional[str] = None
    
    def __str__(self) -> str:
        return f"{self.module}.{self.name}"


class UtilityRegistry:
    """
    Central registry for utility functions to prevent duplication.
    
    This registry tracks all utility functions, their locations, and usage
    to ensure no duplicate implementations exist.
    """
    
    def __init__(self):
        self._functions: Dict[str, FunctionInfo] = {}
        self._categories: Dict[str, Set[str]] = defaultdict(set)
        self._modules: Dict[str, Set[str]] = defaultdict(set)
        self._aliases: Dict[str, str] = {}
        self._duplicates: List[Tuple[str, str]] = []
        
    @protect("register_function", OperationType.COMPUTATION)
    def register(
        self,
        function: Callable,
        category: str,
        description: str,
        aliases: Optional[List[str]] = None,
        deprecated: bool = False,
        replacement: Optional[str] = None
    ) -> None:
        """
        Register a utility function.
        
        Args:
            function: The function to register
            category: Category for the function (e.g., 'memory', 'file_io')
            description: Brief description of what the function does
            aliases: Alternative names for the function
            deprecated: Whether the function is deprecated
            replacement: Name of replacement function if deprecated
        """
        name = function.__name__
        module = function.__module__
        
        # Check for duplicates
        if name in self._functions and self._functions[name].module != module:
            self._duplicates.append((name, module))
            logger.warning(
                f"Duplicate function '{name}' found in {module}. "
                f"Already registered from {self._functions[name].module}"
            )
            return
            
        # Extract function signature
        sig = inspect.signature(function)
        parameters = list(sig.parameters.keys())
        return_type = None
        if sig.return_annotation != inspect.Parameter.empty:
            return_type = str(sig.return_annotation)
            
        # Create function info
        info = FunctionInfo(
            name=name,
            module=module,
            function=function,
            category=category,
            description=description,
            parameters=parameters,
            return_type=return_type,
            aliases=aliases or [],
            deprecated=deprecated,
            replacement=replacement
        )
        
        # Register function
        self._functions[name] = info
        self._categories[category].add(name)
        self._modules[module].add(name)
        
        # Register aliases
        if aliases:
            for alias in aliases:
                self._aliases[alias] = name
                
        logger.debug(f"Registered function: {info}")
        
    @protect("get_function", OperationType.COMPUTATION)
    def get(self, name: str) -> Optional[FunctionInfo]:
        """Get function info by name or alias."""
        # Check if it's an alias
        if name in self._aliases:
            name = self._aliases[name]
            
        return self._functions.get(name)
        
    def get_by_category(self, category: str) -> List[FunctionInfo]:
        """Get all functions in a category."""
        names = self._categories.get(category, set())
        return [self._functions[name] for name in names]
        
    def get_by_module(self, module: str) -> List[FunctionInfo]:
        """Get all functions in a module."""
        names = self._modules.get(module, set())
        return [self._functions[name] for name in names]
        
    def search(self, query: str) -> List[FunctionInfo]:
        """Search for functions by name or description."""
        query = query.lower()
        results = []
        
        for info in self._functions.values():
            if (query in info.name.lower() or 
                query in info.description.lower() or
                any(query in alias.lower() for alias in info.aliases)):
                results.append(info)
                
        return results
        
    def check_duplicates(self) -> List[Tuple[str, str]]:
        """Check for duplicate function names across modules."""
        return self._duplicates
        
    def get_deprecated(self) -> List[FunctionInfo]:
        """Get all deprecated functions."""
        return [info for info in self._functions.values() if info.deprecated]
        
    def get_categories(self) -> List[str]:
        """Get all registered categories."""
        return list(self._categories.keys())
        
    def get_stats(self) -> Dict[str, Any]:
        """Get registry statistics."""
        return {
            'total_functions': len(self._functions),
            'categories': len(self._categories),
            'modules': len(self._modules),
            'aliases': len(self._aliases),
            'duplicates': len(self._duplicates),
            'deprecated': len(self.get_deprecated())
        }
        
    def generate_report(self) -> str:
        """Generate a detailed registry report."""
        lines = [
            "# Utility Registry Report",
            "",
            f"Total Functions: {len(self._functions)}",
            f"Categories: {len(self._categories)}",
            f"Modules: {len(self._modules)}",
            ""
        ]
        
        # Functions by category
        lines.append("## Functions by Category")
        for category in sorted(self._categories.keys()):
            lines.append(f"\n### {category}")
            functions = self.get_by_category(category)
            for func in sorted(functions, key=lambda f: f.name):
                status = " [DEPRECATED]" if func.deprecated else ""
                lines.append(f"- **{func.name}**{status}: {func.description}")
                if func.aliases:
                    lines.append(f"  - Aliases: {', '.join(func.aliases)}")
                if func.replacement:
                    lines.append(f"  - Replacement: {func.replacement}")
                    
        # Duplicates
        if self._duplicates:
            lines.extend([
                "",
                "## Duplicate Functions Found",
                ""
            ])
            for name, module in self._duplicates:
                original = self._functions[name].module
                lines.append(f"- **{name}**: {original} vs {module}")
                
        # Deprecated functions
        deprecated = self.get_deprecated()
        if deprecated:
            lines.extend([
                "",
                "## Deprecated Functions",
                ""
            ])
            for func in deprecated:
                lines.append(f"- **{func.name}** ({func.module})")
                if func.replacement:
                    lines.append(f"  - Use: {func.replacement}")
                    
        return "\n".join(lines)


# Global registry instance
_registry = None


def get_registry() -> UtilityRegistry:
    """Get the global utility registry instance."""
    global _registry
    if _registry is None:
        _registry = UtilityRegistry()
    return _registry


def register_function(
    category: str,
    description: str,
    aliases: Optional[List[str]] = None,
    deprecated: bool = False,
    replacement: Optional[str] = None
):
    """
    Decorator to register a utility function.
    
    Usage:
        @register_function("memory", "Optimize DataFrame memory usage")
        def optimize_dataframe(df):
            ...
    """
    def decorator(func: Callable) -> Callable:
        registry = get_registry()
        registry.register(
            function=func,
            category=category,
            description=description,
            aliases=aliases,
            deprecated=deprecated,
            replacement=replacement
        )
        return func
    return decorator


# Auto-discovery functions
@protect("auto_discover", OperationType.COMPUTATION)
def auto_discover_utilities() -> None:
    """
    Automatically discover and register utility functions.
    
    This function scans the utils module for functions with proper
    docstrings and registers them automatically.
    """
    import importlib
    import pkgutil
    
    utils_path = Path(__file__).parent
    utils_module = "yemen_trade_diagnostic.utils"
    
    # Skip these modules
    skip_modules = {'__init__', 'registry'}
    
    for _, module_name, _ in pkgutil.iter_modules([str(utils_path)]):
        if module_name in skip_modules:
            continue
            
        try:
            # Import module
            full_module_name = f"{utils_module}.{module_name}"
            module = importlib.import_module(full_module_name)
            
            # Scan for functions
            for name, obj in inspect.getmembers(module, inspect.isfunction):
                # Skip private functions
                if name.startswith('_'):
                    continue
                    
                # Skip imported functions
                if obj.__module__ != full_module_name:
                    continue
                    
                # Try to infer category from module name
                category = module_name.replace('_', ' ').title()
                
                # Get description from docstring
                description = "No description available"
                if obj.__doc__:
                    first_line = obj.__doc__.strip().split('\n')[0]
                    if first_line:
                        description = first_line
                        
                # Register the function
                registry = get_registry()
                registry.register(
                    function=obj,
                    category=category,
                    description=description
                )
                
        except Exception as e:
            logger.warning(f"Failed to auto-discover from {module_name}: {e}")


# Validation functions
@protect("validate_no_duplicates", OperationType.COMPUTATION)
def validate_no_duplicates() -> bool:
    """
    Validate that there are no duplicate utility functions.
    
    Returns:
        True if no duplicates found, False otherwise
    """
    registry = get_registry()
    duplicates = registry.check_duplicates()
    
    if duplicates:
        logger.error(f"Found {len(duplicates)} duplicate functions:")
        for name, module in duplicates:
            original = registry.get(name).module
            logger.error(f"  - {name}: {original} vs {module}")
        return False
        
    logger.info("No duplicate functions found")
    return True


@protect("find_similar_functions", OperationType.COMPUTATION)
def find_similar_functions(threshold: float = 0.8) -> List[Tuple[str, str, float]]:
    """
    Find potentially similar functions based on name similarity.
    
    Args:
        threshold: Similarity threshold (0-1)
        
    Returns:
        List of (function1, function2, similarity) tuples
    """
    from difflib import SequenceMatcher
    
    registry = get_registry()
    functions = list(registry._functions.keys())
    similar = []
    
    for i in range(len(functions)):
        for j in range(i + 1, len(functions)):
            name1, name2 = functions[i], functions[j]
            
            # Skip if from same module
            if registry.get(name1).module == registry.get(name2).module:
                continue
                
            # Calculate similarity
            similarity = SequenceMatcher(None, name1, name2).ratio()
            
            if similarity >= threshold:
                similar.append((name1, name2, similarity))
                
    return sorted(similar, key=lambda x: x[2], reverse=True)