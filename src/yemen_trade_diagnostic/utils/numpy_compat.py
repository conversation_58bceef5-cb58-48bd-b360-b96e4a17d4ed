"""
NumPy compatibility module to handle version differences.

This module provides a compatibility layer for numpy to handle
the __version__ attribute issue between numpy 2.x and pandas.
"""

import sys
import importlib


def patch_numpy_version():
    """
    Patch numpy to ensure __version__ attribute exists.
    
    This fixes compatibility issues between numpy 2.x and older pandas versions.
    """
    try:
        import numpy as np
        
        # Check if __version__ exists
        if not hasattr(np, '__version__'):
            # Try to get version from metadata
            try:
                from importlib.metadata import version
                np.__version__ = version('numpy')
            except:
                # Fallback to a default version
                np.__version__ = '2.0.2'
                
        # Also ensure _np_version is set for older compatibility
        if not hasattr(np, '_np_version'):
            np._np_version = np.__version__
            
    except ImportError:
        pass


def ensure_numpy_compatibility():
    """
    Ensure numpy is compatible with the rest of the stack.
    
    This function should be called early in the application startup.
    """
    patch_numpy_version()
    
    # Additional compatibility fixes can be added here
    

# Auto-patch on import
patch_numpy_version()