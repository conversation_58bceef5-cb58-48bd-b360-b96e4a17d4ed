"""
Consolidated file operations utilities for Yemen Trade Diagnostic.

This module combines functionality from file_utils.py and output_utils.py,
providing a single source of truth for all file and directory operations.
"""

# Standard library imports
import csv
import json
import os
import pickle
import tempfile
from datetime import date, datetime, time
from decimal import Decimal
from pathlib import Path
from typing import Any, Callable, Dict, Generic, List, Optional, TypeVar, Union

# Third-party imports
import numpy as np
import pandas as pd

# Project imports
from yemen_trade_diagnostic.errors import (
    ErrorCategory,
    ErrorSeverity,
    with_error_handling,
)
from yemen_trade_diagnostic.interfaces.logging_interface import get_logger

# Get logger
logger = get_logger(__name__)


# ===========================
# Directory Operations
# ===========================

def ensure_dir_exists(path: Union[str, Path]) -> Path:
    """
    Ensure a directory exists, creating it if necessary.
    
    Args:
        path: Directory path
        
    Returns:
        Path: Path object for the directory
        
    Raises:
        OSError: If directory creation fails
    """
    path_obj = Path(path)
    path_obj.mkdir(parents=True, exist_ok=True)
    return path_obj


def ensure_directory_exists(path: Union[str, Path]) -> Path:
    """
    Alias for ensure_dir_exists to maintain backward compatibility.
    
    Args:
        path: Directory path
        
    Returns:
        Path: Path object for the directory
    """
    return ensure_dir_exists(path)


def list_files(directory: Union[str, Path], 
               pattern: Optional[str] = None,
               recursive: bool = False) -> List[Path]:
    """
    List all files in a directory, optionally filtering by pattern.
    
    Args:
        directory: Directory path
        pattern: Optional glob pattern to filter files (e.g., '*.csv')
        recursive: Whether to search recursively
        
    Returns:
        List of file paths
    """
    dir_path = Path(directory)
    if not dir_path.exists():
        logger.warning(f"Directory does not exist: {dir_path}")
        return []
    
    if pattern:
        if recursive:
            return list(dir_path.rglob(pattern))
        else:
            return list(dir_path.glob(pattern))
    else:
        if recursive:
            return [p for p in dir_path.rglob('*') if p.is_file()]
        else:
            return [p for p in dir_path.iterdir() if p.is_file()]


def list_subdirectories(directory: Union[str, Path], 
                       recursive: bool = False) -> List[Path]:
    """
    List all subdirectories in a directory.
    
    Args:
        directory: Directory path
        recursive: Whether to search recursively
        
    Returns:
        List of subdirectory paths
    """
    dir_path = Path(directory)
    if not dir_path.exists():
        logger.warning(f"Directory does not exist: {dir_path}")
        return []
    
    if recursive:
        return [p for p in dir_path.rglob('*') if p.is_dir()]
    else:
        return [p for p in dir_path.iterdir() if p.is_dir()]


# ===========================
# File Operations
# ===========================

def check_file_exists(filepath: Union[str, Path]) -> bool:
    """
    Check if a file exists.
    
    Args:
        filepath: Path to the file
        
    Returns:
        True if the file exists, False otherwise
    """
    return Path(filepath).exists()


def get_file_extension(filepath: Union[str, Path]) -> str:
    """
    Get the file extension (without the dot).
    
    Args:
        filepath: Path to the file
        
    Returns:
        File extension as string (e.g., 'csv', 'json')
    """
    return Path(filepath).suffix.lstrip('.')


def get_file_modification_time(filepath: Union[str, Path]) -> datetime:
    """
    Get the modification time of a file as a datetime object.
    
    Args:
        filepath: Path to the file
        
    Returns:
        Modification time as datetime
        
    Raises:
        FileNotFoundError: If the file does not exist
    """
    path_obj = Path(filepath)
    if not path_obj.exists():
        raise FileNotFoundError(f"File not found: {filepath}")
    
    timestamp = path_obj.stat().st_mtime
    return datetime.fromtimestamp(timestamp)


def is_file_newer_than(file1: Union[str, Path], 
                      file2: Union[str, Path]) -> bool:
    """
    Check if file1 is newer than file2.
    
    Args:
        file1: Path to the first file
        file2: Path to the second file
        
    Returns:
        True if file1 is newer than file2, False otherwise
        
    Raises:
        FileNotFoundError: If either file does not exist
    """
    time1 = get_file_modification_time(file1)
    time2 = get_file_modification_time(file2)
    return time1 > time2


# ===========================
# Save Operations
# ===========================

def save_dataframe_to_csv(df: pd.DataFrame, filepath: Union[str, Path], 
                         **csv_kwargs) -> bool:
    """
    Save a pandas DataFrame to a CSV file.
    
    Args:
        df: DataFrame to save
        filepath: Path to save the file to
        **csv_kwargs: Additional keyword arguments to pass to pd.DataFrame.to_csv()
        
    Returns:
        bool: True if the operation succeeded
        
    Raises:
        OSError: If the file cannot be written
    """
    # Ensure directory exists
    filepath_obj = Path(filepath)
    ensure_dir_exists(filepath_obj.parent)
    
    # Save the file
    df.to_csv(filepath_obj, **csv_kwargs)
    logger.info(f"Saved DataFrame to CSV: {filepath_obj}")
    return True


def save_dataframe_to_json(df: pd.DataFrame, filepath: Union[str, Path],
                          orient: str = 'records', **json_kwargs) -> bool:
    """
    Save a pandas DataFrame to a JSON file.
    
    Args:
        df: DataFrame to save
        filepath: Path to save the file to
        orient: JSON orientation for DataFrame
        **json_kwargs: Additional keyword arguments to pass to pd.DataFrame.to_json()
        
    Returns:
        bool: True if the operation succeeded
        
    Raises:
        OSError: If the file cannot be written
    """
    # Ensure directory exists
    filepath_obj = Path(filepath)
    ensure_dir_exists(filepath_obj.parent)
    
    # Save the file
    df.to_json(filepath_obj, orient=orient, **json_kwargs)
    logger.info(f"Saved DataFrame to JSON: {filepath_obj}")
    return True


def save_dataframe_to_parquet(df: pd.DataFrame, filepath: Union[str, Path],
                             **parquet_kwargs) -> bool:
    """
    Save a pandas DataFrame to a Parquet file.
    
    Args:
        df: DataFrame to save
        filepath: Path to save the file to
        **parquet_kwargs: Additional keyword arguments to pass to pd.DataFrame.to_parquet()
        
    Returns:
        bool: True if the operation succeeded
        
    Raises:
        OSError: If the file cannot be written
        ImportError: If pyarrow is not installed
    """
    # Ensure directory exists
    filepath_obj = Path(filepath)
    ensure_dir_exists(filepath_obj.parent)
    
    # Save the file
    df.to_parquet(filepath_obj, **parquet_kwargs)
    logger.info(f"Saved DataFrame to Parquet: {filepath_obj}")
    return True


def save_dict_to_json(data: Dict[str, Any], filepath: Union[str, Path],
                     indent: int = 2, **json_kwargs) -> bool:
    """
    Save a dictionary to a JSON file with custom serialization support.
    
    Args:
        data: Dictionary to save
        filepath: Path to save the file to
        indent: JSON indentation level
        **json_kwargs: Additional keyword arguments to pass to json.dump()
        
    Returns:
        bool: True if the operation succeeded
        
    Raises:
        OSError: If the file cannot be written
    """
    # Ensure directory exists
    filepath_obj = Path(filepath)
    ensure_dir_exists(filepath_obj.parent)
    
    # Use atomic write for safety
    with tempfile.NamedTemporaryFile(mode='w', dir=filepath_obj.parent,
                                   delete=False) as temp_file:
        try:
            json.dump(data, temp_file, indent=indent, 
                     default=_default_serializer, **json_kwargs)
            temp_file.flush()
            os.fsync(temp_file.fileno())
            
            # Atomic move
            temp_path = Path(temp_file.name)
            temp_path.replace(filepath_obj)
            
            logger.info(f"Saved dictionary to JSON: {filepath_obj}")
            return True
        except Exception as e:
            # Clean up temp file on error
            Path(temp_file.name).unlink(missing_ok=True)
            raise


def save_data_to_pickle(data: Any, filepath: Union[str, Path]) -> bool:
    """
    Save data to a pickle file.
    
    Args:
        data: Data to save (any pickleable object)
        filepath: Path to save the file to
        
    Returns:
        bool: True if the operation succeeded
        
    Raises:
        OSError: If the file cannot be written
    """
    # Ensure directory exists
    filepath_obj = Path(filepath)
    ensure_dir_exists(filepath_obj.parent)
    
    # Save the file
    with open(filepath_obj, 'wb') as f:
        pickle.dump(data, f)
    
    logger.info(f"Saved data to pickle: {filepath_obj}")
    return True


def save_dataframe(df: pd.DataFrame, filepath: Union[str, Path], 
                  format: Optional[str] = None, **kwargs) -> bool:
    """
    Save a DataFrame in the specified format, auto-detecting from extension if not provided.
    
    Args:
        df: DataFrame to save
        filepath: Path to save the file to
        format: File format ('csv', 'json', 'parquet'). If None, auto-detect from extension
        **kwargs: Additional keyword arguments for the specific save function
        
    Returns:
        bool: True if the operation succeeded
        
    Raises:
        ValueError: If the format is not supported
        OSError: If the file cannot be written
    """
    filepath_obj = Path(filepath)
    
    # Auto-detect format from extension if not provided
    if format is None:
        format = get_file_extension(filepath_obj)
    
    format = format.lower()
    
    if format == 'csv':
        return save_dataframe_to_csv(df, filepath_obj, **kwargs)
    elif format == 'json':
        return save_dataframe_to_json(df, filepath_obj, **kwargs)
    elif format == 'parquet':
        return save_dataframe_to_parquet(df, filepath_obj, **kwargs)
    else:
        raise ValueError(f"Unsupported format: {format}. "
                        f"Supported formats are: csv, json, parquet")


def save_data_to_multiple_formats(data: Union[pd.DataFrame, Dict[str, Any]], 
                                 base_filepath: Union[str, Path],
                                 formats: List[str]) -> Dict[str, bool]:
    """
    Save data to multiple file formats.
    
    Args:
        data: Data to save (DataFrame or dictionary)
        base_filepath: Base filepath without extension
        formats: List of formats to save in ('csv', 'json', 'parquet', 'pickle')
        
    Returns:
        Dictionary mapping format to success status
    """
    base_path = Path(base_filepath)
    results = {}
    
    for format in formats:
        filepath = base_path.with_suffix(f'.{format}')
        try:
            if isinstance(data, pd.DataFrame):
                if format in ['csv', 'json', 'parquet']:
                    results[format] = save_dataframe(data, filepath, format=format)
                elif format == 'pickle':
                    results[format] = save_data_to_pickle(data, filepath)
                else:
                    logger.warning(f"Unsupported format for DataFrame: {format}")
                    results[format] = False
            elif isinstance(data, dict):
                if format == 'json':
                    results[format] = save_dict_to_json(data, filepath)
                elif format == 'pickle':
                    results[format] = save_data_to_pickle(data, filepath)
                else:
                    logger.warning(f"Unsupported format for dictionary: {format}")
                    results[format] = False
            else:
                if format == 'pickle':
                    results[format] = save_data_to_pickle(data, filepath)
                else:
                    logger.warning(f"Unsupported format for {type(data).__name__}: {format}")
                    results[format] = False
        except Exception as e:
            logger.error(f"Failed to save to {format}: {e}")
            results[format] = False
    
    return results


# ===========================
# Load Operations
# ===========================

def load_dataframe_from_csv(filepath: Union[str, Path], **csv_kwargs) -> pd.DataFrame:
    """
    Load a pandas DataFrame from a CSV file.
    
    Args:
        filepath: Path to the CSV file
        **csv_kwargs: Additional keyword arguments to pass to pd.read_csv()
        
    Returns:
        Loaded DataFrame
        
    Raises:
        FileNotFoundError: If the file does not exist
        pd.errors.ParserError: If the CSV cannot be parsed
    """
    filepath_obj = Path(filepath)
    if not filepath_obj.exists():
        raise FileNotFoundError(f"CSV file not found: {filepath}")
    
    df = pd.read_csv(filepath_obj, **csv_kwargs)
    logger.info(f"Loaded DataFrame from CSV: {filepath_obj} (shape: {df.shape})")
    return df


def load_dataframe_from_json(filepath: Union[str, Path], **json_kwargs) -> pd.DataFrame:
    """
    Load a pandas DataFrame from a JSON file.
    
    Args:
        filepath: Path to the JSON file
        **json_kwargs: Additional keyword arguments to pass to pd.read_json()
        
    Returns:
        Loaded DataFrame
        
    Raises:
        FileNotFoundError: If the file does not exist
        ValueError: If the JSON cannot be parsed as a DataFrame
    """
    filepath_obj = Path(filepath)
    if not filepath_obj.exists():
        raise FileNotFoundError(f"JSON file not found: {filepath}")
    
    df = pd.read_json(filepath_obj, **json_kwargs)
    logger.info(f"Loaded DataFrame from JSON: {filepath_obj} (shape: {df.shape})")
    return df


def load_dict_from_json(filepath: Union[str, Path]) -> Dict[str, Any]:
    """
    Load a dictionary from a JSON file.
    
    Args:
        filepath: Path to the JSON file
        
    Returns:
        Loaded dictionary
        
    Raises:
        FileNotFoundError: If the file does not exist
        json.JSONDecodeError: If the JSON cannot be parsed
    """
    filepath_obj = Path(filepath)
    if not filepath_obj.exists():
        raise FileNotFoundError(f"JSON file not found: {filepath}")
    
    with open(filepath_obj, 'r') as f:
        data = json.load(f)
    
    logger.info(f"Loaded dictionary from JSON: {filepath_obj}")
    return data


def load_data_from_pickle(filepath: Union[str, Path]) -> Any:
    """
    Load data from a pickle file.
    
    Args:
        filepath: Path to the pickle file
        
    Returns:
        Loaded data
        
    Raises:
        FileNotFoundError: If the file does not exist
        pickle.UnpicklingError: If the pickle cannot be loaded
    """
    filepath_obj = Path(filepath)
    if not filepath_obj.exists():
        raise FileNotFoundError(f"Pickle file not found: {filepath}")
    
    with open(filepath_obj, 'rb') as f:
        data = pickle.load(f)
    
    logger.info(f"Loaded data from pickle: {filepath_obj}")
    return data


# ===========================
# Output Path Management
# ===========================

def get_output_dir(pipeline_name: str, year: Optional[int] = None) -> Path:
    """
    Get the output directory for a pipeline and optionally a year.
    
    Args:
        pipeline_name: Name of the pipeline
        year: Optional year for analysis
        
    Returns:
        Path to the output directory
    """
    output_dir = Path("output") / pipeline_name
    if year is not None:
        output_dir = output_dir / str(year)
    return output_dir


def get_output_file_path(pipeline_name: str, output_name: str, 
                        year: Optional[int] = None, 
                        extension: str = "json") -> Path:
    """
    Get the full path for an output file.
    
    Args:
        pipeline_name: Name of the pipeline
        output_name: Name of the output
        year: Optional year for analysis
        extension: File extension (default: json)
        
    Returns:
        Path to the output file
    """
    output_dir = get_output_dir(pipeline_name, year)
    
    if year is not None:
        filename = f"{output_name}_{year}.{extension}"
    else:
        filename = f"{output_name}.{extension}"
    
    return output_dir / filename


# ===========================
# Utility Functions
# ===========================

def convert_dataframe_to_serializable_list(df: pd.DataFrame) -> List[Dict[str, Any]]:
    """
    Convert a DataFrame to a list of dictionaries that can be serialized to JSON.
    
    Args:
        df: DataFrame to convert
        
    Returns:
        List of dictionaries
    """
    # Handle NaN/None values and convert to Python types
    df_copy = df.copy()
    
    # Replace NaN with None for JSON compatibility
    df_copy = df_copy.where(pd.notnull(df_copy), None)
    
    # Convert to list of dictionaries
    records = df_copy.to_dict('records')
    
    # Ensure all values are JSON serializable
    for record in records:
        for key, value in record.items():
            if isinstance(value, (np.integer, np.floating)):
                record[key] = value.item()
            elif isinstance(value, np.ndarray):
                record[key] = value.tolist()
            elif pd.isna(value):
                record[key] = None
    
    return records


def _default_serializer(obj: Any) -> Any:
    """
    Default JSON serializer for non-standard types.
    
    Args:
        obj: Object to serialize
        
    Returns:
        JSON-serializable representation
        
    Raises:
        TypeError: If the object cannot be serialized
    """
    if isinstance(obj, (datetime, date, time)):
        return obj.isoformat()
    elif isinstance(obj, Decimal):
        return float(obj)
    elif isinstance(obj, (np.integer, np.floating)):
        return obj.item()
    elif isinstance(obj, np.ndarray):
        return obj.tolist()
    elif isinstance(obj, pd.DataFrame):
        return convert_dataframe_to_serializable_list(obj)
    elif isinstance(obj, pd.Series):
        return obj.to_dict()
    elif hasattr(obj, '__dict__'):
        return obj.__dict__
    else:
        raise TypeError(f"Object of type {type(obj).__name__} is not JSON serializable")


# ===========================
# Backward Compatibility
# ===========================

# These imports allow existing code to continue working
# They should be considered deprecated and will be removed in a future version

__all__ = [
    # Directory operations
    'ensure_dir_exists',
    'ensure_directory_exists',
    'list_files',
    'list_subdirectories',
    # File operations
    'check_file_exists',
    'get_file_extension',
    'get_file_modification_time',
    'is_file_newer_than',
    # Save operations
    'save_dataframe_to_csv',
    'save_dataframe_to_json',
    'save_dataframe_to_parquet',
    'save_dict_to_json',
    'save_data_to_pickle',
    'save_dataframe',
    'save_data_to_multiple_formats',
    # Load operations
    'load_dataframe_from_csv',
    'load_dataframe_from_json',
    'load_dict_from_json',
    'load_data_from_pickle',
    # Output path management
    'get_output_dir',
    'get_output_file_path',
    # Utility functions
    'convert_dataframe_to_serializable_list',
]