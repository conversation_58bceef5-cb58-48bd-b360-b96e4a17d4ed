"""Consolidated memory management utilities for the Yemen Trade Diagnostic system.

This module combines memory optimization and memory utility functions from:
- memory_optimizer.py: DataFrame memory optimization and chunking
- memory_utils.py: Advanced DataFrame optimization with sparse/category support

Provides comprehensive memory management capabilities including:
- DataFrame memory optimization (downcasting, categoricals, sparse arrays)
- Chunk processing for large datasets
- Memory-aware file reading (CSV, Parquet, JSON)
- Memory pressure monitoring
- Backward compatibility with both legacy modules
"""
# Standard library imports
import gc
import warnings
from pathlib import Path
from typing import Any, Callable, Dict, Generator, Iterable, List, Optional, Tuple, Union

# Third-party imports
import numpy as np
import pandas as pd
import psutil

# Project imports
from yemen_trade_diagnostic.interfaces.logging_interface import get_logger

logger = get_logger(__name__)


# ============================================================================
# Core Memory Optimization Functions
# ============================================================================

def optimize_dataframe(
    df: pd.DataFrame, 
    category_threshold: float = 0.5, 
    inplace: bool = False,
    memory_options: Optional[Dict[str, Any]] = None
) -> Tuple[Optional[pd.DataFrame], Dict[str, float]]:
    """
    Optimizes DataFrame memory usage with advanced techniques.
    
    This is the primary memory optimization function that combines functionality
    from both memory_utils and memory_optimizer. It includes:
    - Downcasting numeric types
    - Converting low-cardinality object columns to category
    - Converting high-zero columns to sparse arrays
    
    Args:
        df: The pandas DataFrame to optimize
        category_threshold: Threshold for converting object columns to category
                          (unique_values / total_values < threshold)
        inplace: If True, performs optimization in-place
        memory_options: Additional options including 'sparse_threshold'
        
    Returns:
        Tuple of (optimized_df or None if inplace, stats_dict)
    """
    if df is None or df.empty:
        return (None, {}) if inplace else (df, {})
    
    df_to_optimize = df if inplace else df.copy()
    
    stats = {
        'initial_mb': 0,
        'final_mb': 0,
        'total_savings_mb': 0,
        'savings_by_downcast_integer_mb': 0,
        'savings_by_downcast_float_mb': 0,
        'savings_by_categorical_mb': 0,
        'savings_by_sparse_mb': 0
    }
    
    stats['initial_mb'] = df_to_optimize.memory_usage(deep=True).sum() / (1024**2)
    logger.info(f"Initial DataFrame memory usage: {stats['initial_mb']:.2f} MB")
    
    if memory_options is None:
        memory_options = {}
    
    sparse_threshold = memory_options.get('sparse_threshold', 0.75)
    
    for col in df_to_optimize.columns:
        # Skip if column is a DataFrame
        if isinstance(df_to_optimize[col], pd.DataFrame):
            continue
            
        col_dtype = df_to_optimize[col].dtype
        mem_before_col_bytes = df_to_optimize[col].memory_usage(deep=True)
        
        # Object to category conversion
        if col_dtype == "object" and category_threshold > 0:
            if not pd.api.types.is_categorical_dtype(df_to_optimize[col]):
                num_unique = len(df_to_optimize[col].unique())
                num_total = len(df_to_optimize[col])
                if num_unique > 0 and num_total > 0 and num_unique / num_total < category_threshold:
                    try:
                        df_to_optimize[col] = df_to_optimize[col].astype('category')
                        mem_after = df_to_optimize[col].memory_usage(deep=True)
                        stats['savings_by_categorical_mb'] += (mem_before_col_bytes - mem_after) / (1024**2)
                        logger.debug(f"Column '{col}' converted to category")
                    except Exception as e:
                        logger.warning(f"Could not convert column '{col}' to category: {e}")
        
        # Integer optimization
        elif pd.api.types.is_integer_dtype(col_dtype):
            c_min = df_to_optimize[col].min()
            c_max = df_to_optimize[col].max()
            
            if not pd.isna(c_min) and not pd.isna(c_max):
                # Unsigned integers
                if c_min >= 0:
                    if c_max <= 255:
                        df_to_optimize[col] = df_to_optimize[col].astype(np.uint8)
                    elif c_max <= 65535:
                        df_to_optimize[col] = df_to_optimize[col].astype(np.uint16)
                    elif c_max <= 4294967295:
                        df_to_optimize[col] = df_to_optimize[col].astype(np.uint32)
                    else:
                        df_to_optimize[col] = df_to_optimize[col].astype(np.uint64)
                # Signed integers
                else:
                    if c_min >= -128 and c_max <= 127:
                        df_to_optimize[col] = df_to_optimize[col].astype(np.int8)
                    elif c_min >= -32768 and c_max <= 32767:
                        df_to_optimize[col] = df_to_optimize[col].astype(np.int16)
                    elif c_min >= -2147483648 and c_max <= 2147483647:
                        df_to_optimize[col] = df_to_optimize[col].astype(np.int32)
                    else:
                        df_to_optimize[col] = df_to_optimize[col].astype(np.int64)
                
                mem_after = df_to_optimize[col].memory_usage(deep=True)
                savings = (mem_before_col_bytes - mem_after) / (1024**2)
                if savings > 0:
                    stats['savings_by_downcast_integer_mb'] += savings
        
        # Float optimization
        elif pd.api.types.is_float_dtype(col_dtype):
            c_min = df_to_optimize[col].min()
            c_max = df_to_optimize[col].max()
            
            if not pd.isna(c_min) and not pd.isna(c_max):
                if c_min >= np.finfo(np.float32).min and c_max <= np.finfo(np.float32).max:
                    df_to_optimize[col] = df_to_optimize[col].astype(np.float32)
                    mem_after = df_to_optimize[col].memory_usage(deep=True)
                    savings = (mem_before_col_bytes - mem_after) / (1024**2)
                    if savings > 0:
                        stats['savings_by_downcast_float_mb'] += savings
        
        # Sparse array optimization for numeric columns
        current_dtype = df_to_optimize[col].dtype
        if (pd.api.types.is_numeric_dtype(current_dtype) and 
            not pd.api.types.is_categorical_dtype(current_dtype) and
            len(df_to_optimize[col]) > 0):
            
            zero_proportion = (df_to_optimize[col] == 0).sum() / len(df_to_optimize[col])
            if not isinstance(current_dtype, pd.SparseDtype) and zero_proportion > sparse_threshold:
                try:
                    # Re-measure memory if dtype changed
                    mem_before_sparse = df_to_optimize[col].memory_usage(deep=True)
                    df_to_optimize[col] = df_to_optimize[col].astype(pd.SparseDtype(current_dtype, fill_value=0))
                    mem_after_sparse = df_to_optimize[col].memory_usage(deep=True)
                    stats['savings_by_sparse_mb'] += (mem_before_sparse - mem_after_sparse) / (1024**2)
                    logger.info(f"Column '{col}' converted to sparse array")
                except Exception as e:
                    logger.warning(f"Could not convert column '{col}' to sparse: {e}")
    
    stats['final_mb'] = df_to_optimize.memory_usage(deep=True).sum() / (1024**2)
    stats['total_savings_mb'] = stats['initial_mb'] - stats['final_mb']
    
    logger.info(f"Optimized DataFrame memory usage: {stats['final_mb']:.2f} MB. "
                f"Total saved: {stats['total_savings_mb']:.2f} MB")
    
    if inplace:
        return None, stats
    return df_to_optimize, stats


def optimize_dataframe_dtypes(df: pd.DataFrame) -> pd.DataFrame:
    """
    Legacy function for basic DataFrame memory optimization.
    
    This function provides backward compatibility with memory_optimizer.py.
    For new code, use optimize_dataframe() which provides more features.
    
    Args:
        df: DataFrame to optimize
        
    Returns:
        Optimized DataFrame
    """
    if df is None or df.empty:
        return df
    
    # Use the advanced optimization with conservative settings
    optimized_df, _ = optimize_dataframe(
        df, 
        category_threshold=0,  # Disable category conversion for compatibility
        inplace=False,
        memory_options={'sparse_threshold': 1.0}  # Disable sparse conversion
    )
    
    return optimized_df


# ============================================================================
# Chunk Processing Functions
# ============================================================================

def process_dataframe_in_chunks(
    df: pd.DataFrame,
    func: Optional[Callable] = None,
    processing_func: Optional[Callable] = None,
    combine_func: Optional[Callable] = None,
    chunk_size: int = 100000,
    **kwargs
) -> Any:
    """
    Process a large DataFrame in chunks to reduce memory usage.
    
    Supports both 'func' and 'processing_func' parameters for backward compatibility.
    
    Args:
        df: DataFrame to process
        func: Function to apply to each chunk (legacy parameter)
        processing_func: Function to apply to each chunk (preferred)
        combine_func: Function to combine results from chunks
        chunk_size: Number of rows per chunk
        **kwargs: Additional arguments for the processing function
        
    Returns:
        Combined result from processing all chunks
    """
    if df is None or df.empty:
        return df
    
    # Handle both parameter names for compatibility
    process_fn = processing_func if processing_func is not None else func
    if process_fn is None:
        logger.error("No processing function provided to process_dataframe_in_chunks")
        return df
    
    num_chunks = (len(df) + chunk_size - 1) // chunk_size
    results = []
    
    logger.info(f"Processing DataFrame with {len(df)} rows in {num_chunks} chunks")
    
    for i in range(num_chunks):
        start_idx = i * chunk_size
        end_idx = min((i + 1) * chunk_size, len(df))
        chunk = df.iloc[start_idx:end_idx]
        
        logger.debug(f"Processing chunk {i+1}/{num_chunks}")
        result = process_fn(chunk, **kwargs)
        
        if result is not None:
            results.append(result)
    
    # Combine results
    if not results:
        return None
    
    if combine_func is not None:
        return combine_func(results)
    elif isinstance(results[0], pd.DataFrame):
        return safely_concatenate_dfs(results)
    elif isinstance(results[0], dict):
        combined_dict = {}
        for result in results:
            combined_dict.update(result)
        return combined_dict
    else:
        return results


def process_in_chunks_v2(
    data_source: Union[pd.DataFrame, Iterable[pd.DataFrame]],
    processing_func: Callable[[pd.DataFrame], Any],
    chunksize_for_df_input: Optional[int] = None,
    *args,
    **kwargs
) -> Generator[Any, None, None]:
    """
    Process data in chunks from DataFrame or iterable source.
    
    Args:
        data_source: DataFrame or iterable of DataFrames
        processing_func: Function to apply to each chunk
        chunksize_for_df_input: Chunk size if data_source is a DataFrame
        *args, **kwargs: Additional arguments for processing_func
        
    Yields:
        Results from processing each chunk
    """
    if isinstance(data_source, pd.DataFrame):
        if chunksize_for_df_input is None or chunksize_for_df_input <= 0:
            logger.warning("No chunk size specified, processing as single chunk")
            yield processing_func(data_source, *args, **kwargs)
            return
        
        num_rows = len(data_source)
        for start_row in range(0, num_rows, chunksize_for_df_input):
            end_row = min(start_row + chunksize_for_df_input, num_rows)
            chunk = data_source.iloc[start_row:end_row]
            logger.debug(f"Processing chunk rows {start_row}-{end_row-1}")
            yield processing_func(chunk, *args, **kwargs)
            
    elif hasattr(data_source, '__iter__') and not isinstance(data_source, str):
        for i, chunk in enumerate(data_source):
            if not isinstance(chunk, pd.DataFrame):
                logger.error(f"Non-DataFrame element at index {i}")
                continue
            logger.debug(f"Processing chunk {i+1} from iterable")
            yield processing_func(chunk, *args, **kwargs)
    else:
        raise TypeError("data_source must be a DataFrame or iterable of DataFrames")


def chunk_generator(df: pd.DataFrame, chunk_size: int = 100000) -> Generator[pd.DataFrame, None, None]:
    """
    Generate chunks of a DataFrame with automatic garbage collection.
    
    Args:
        df: DataFrame to chunk
        chunk_size: Number of rows per chunk
        
    Yields:
        DataFrame chunks
    """
    if df is None or df.empty:
        yield df
        return
    
    num_chunks = (len(df) + chunk_size - 1) // chunk_size
    logger.debug(f"Chunking DataFrame into {num_chunks} chunks of {chunk_size} rows")
    
    for i in range(num_chunks):
        start_idx = i * chunk_size
        end_idx = min((i + 1) * chunk_size, len(df))
        
        yield df.iloc[start_idx:end_idx].copy()
        
        # Force garbage collection after each yield
        gc.collect()


# ============================================================================
# File Reading Functions
# ============================================================================

def read_csv_optimized_v2(
    file_path: Union[str, Path],
    dtype_spec: Optional[Dict[str, Any]] = None,
    usecols: Optional[List[str]] = None,
    chunksize: Optional[int] = None,
    low_memory: bool = True,
    apply_optimize_df: bool = True,
    category_threshold_for_optimize: float = 0.5,
    memory_options_for_optimize: Optional[Dict[str, Any]] = None,
    **kwargs
) -> Union[Tuple[pd.DataFrame, Optional[Dict[str, float]]], pd.io.parsers.readers.TextFileReader]:
    """
    Read CSV file with memory optimizations.
    
    Args:
        file_path: Path to CSV file
        dtype_spec: Column data types
        usecols: Columns to read
        chunksize: If specified, returns iterator
        low_memory: pandas low_memory option
        apply_optimize_df: Whether to apply optimize_dataframe
        category_threshold_for_optimize: Threshold for category conversion
        memory_options_for_optimize: Memory optimization options
        **kwargs: Additional pandas read_csv arguments
        
    Returns:
        (DataFrame, stats) tuple or TextFileReader if chunksize specified
    """
    logger.info(f"Reading CSV optimized: {file_path}")
    
    read_params = {
        'dtype': dtype_spec,
        'usecols': usecols,
        'low_memory': low_memory,
        'delimiter': ',',
        'header': 0,
        'comment': '#'
    }
    
    # Add valid pandas kwargs
    pandas_valid_kwargs = ['sep', 'delimiter', 'header', 'names', 'index_col', 
                          'encoding', 'skiprows', 'nrows', 'comment']
    for k, v in kwargs.items():
        if k in pandas_valid_kwargs:
            read_params[k] = v
    
    if chunksize:
        logger.info(f"Returning iterator for chunked reading (chunksize={chunksize})")
        return pd.read_csv(file_path, chunksize=chunksize, **read_params)
    
    try:
        df = pd.read_csv(file_path, **read_params)
        logger.info(f"CSV read into memory. Shape: {df.shape}")
        
        if apply_optimize_df:
            return optimize_dataframe(
                df,
                category_threshold=category_threshold_for_optimize,
                inplace=False,
                memory_options=memory_options_for_optimize
            )
        else:
            return df, None
            
    except FileNotFoundError:
        logger.error(f"File not found: {file_path}")
        raise
    except Exception as e:
        logger.error(f"Error reading CSV {file_path}: {e}")
        raise


def read_parquet_optimized_v2(
    file_path: Union[str, Path],
    usecols: Optional[List[str]] = None,
    apply_optimize_df: bool = True,
    category_threshold_for_optimize: float = 0.5,
    memory_options_for_optimize: Optional[Dict[str, Any]] = None,
    **kwargs
) -> Tuple[pd.DataFrame, Optional[Dict[str, float]]]:
    """Read Parquet file with optional memory optimizations."""
    logger.info(f"Reading Parquet optimized: {file_path}")
    
    pandas_valid_kwargs = ['columns', 'engine', 'filters', 'use_nullable_dtypes', 
                          'dtype_backend', 'storage_options', 'filesystem']
    read_params = {'columns': usecols}
    for k, v in kwargs.items():
        if k in pandas_valid_kwargs:
            read_params[k] = v
    
    try:
        df = pd.read_parquet(file_path, **read_params)
        logger.info(f"Parquet read into memory. Shape: {df.shape}")
        
        if apply_optimize_df:
            return optimize_dataframe(
                df,
                category_threshold=category_threshold_for_optimize,
                inplace=False,
                memory_options=memory_options_for_optimize
            )
        else:
            return df, None
            
    except Exception as e:
        logger.error(f"Error reading Parquet {file_path}: {e}")
        raise


# ============================================================================
# Memory Monitoring Functions
# ============================================================================

def get_memory_usage_stats() -> Dict[str, Any]:
    """Get system memory usage statistics."""
    try:
        mem = psutil.virtual_memory()
        return {
            "total_gb": mem.total / (1024**3),
            "available_gb": mem.available / (1024**3),
            "percent_used": mem.percent,
            "used_gb": mem.used / (1024**3),
            "free_gb": mem.free / (1024**3),
        }
    except Exception as e:
        logger.warning(f"Cannot get memory stats: {e}")
        return {"error": str(e), "percent_used": 0}


def is_under_memory_pressure(threshold_percent: float = 80.0) -> bool:
    """Check if system is under memory pressure."""
    stats = get_memory_usage_stats()
    if "error" in stats:
        return False
    
    is_pressure = stats["percent_used"] > threshold_percent
    if is_pressure:
        logger.warning(f"Memory pressure detected: {stats['percent_used']}% used")
    return is_pressure


def get_memory_usage(df: pd.DataFrame, deep: bool = True, format_as_mb: bool = False) -> Union[int, float]:
    """
    Get DataFrame memory usage with compatibility handling.
    
    Args:
        df: DataFrame to measure
        deep: Whether to calculate deep memory usage
        format_as_mb: If True, returns result in MB
        
    Returns:
        Memory usage in bytes or MB
    """
    try:
        with warnings.catch_warnings():
            warnings.simplefilter("ignore", FutureWarning)
            warnings.simplefilter("ignore", DeprecationWarning)
            memory_usage = df.memory_usage(deep=deep).sum()
    except Exception as e:
        logger.warning(f"Error getting memory usage: {e}")
        try:
            memory_usage = df.memory_usage().sum()
            if deep:
                # Manual calculation for object columns
                for col in df.select_dtypes(include=['object']).columns:
                    try:
                        memory_usage += df[col].apply(lambda x: x.__sizeof__()).sum()
                    except:
                        pass
        except Exception as e2:
            logger.error(f"Failed to calculate memory usage: {e2}")
            return 0
    
    if format_as_mb:
        return memory_usage / (1024**2)
    
    return memory_usage


# ============================================================================
# Utility Functions
# ============================================================================

def safely_concatenate_dfs(dfs: List[pd.DataFrame], **kwargs) -> pd.DataFrame:
    """Safely concatenate DataFrames, handling empty or None values."""
    valid_dfs = [df for df in dfs if df is not None and not df.empty]
    
    if not valid_dfs:
        return pd.DataFrame()
    
    return pd.concat(valid_dfs, **kwargs)


def sample_large_dataframe(df: pd.DataFrame, sample_size: int = 10000) -> pd.DataFrame:
    """Sample a large DataFrame for exploratory analysis."""
    if df is None or df.empty or len(df) <= sample_size:
        return df
    
    return df.sample(sample_size, random_state=42)


# ============================================================================
# Backward Compatibility Aliases
# ============================================================================

# Aliases for memory_utils.py functions
get_memory_usage_stats_v2 = get_memory_usage_stats
is_under_memory_pressure_v2 = is_under_memory_pressure

# For functions that had different names in memory_optimizer.py
optimize_memory_usage = optimize_dataframe_dtypes