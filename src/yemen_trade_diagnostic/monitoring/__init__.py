"""
Performance Monitoring System for Yemen Trade Diagnostic.

This module provides comprehensive performance monitoring capabilities including:
- Function execution time tracking
- Memory usage monitoring
- CPU/GPU utilization tracking
- Performance visualization and dashboards
- Automated report generation
- Anomaly detection and alerting

Quick Start:
    from yemen_trade_diagnostic.monitoring import monitor_performance
    
    @monitor_performance(track_args=True, log_threshold=1.0)
    def process_data(df):
        # Your processing logic
        return df

Advanced Usage:
    from yemen_trade_diagnostic.monitoring import (
        PerformanceMonitor,
        PerformanceVisualizer,
        PerformanceReporter,
        get_monitoring_config
    )
    
    # Get global monitor instance
    monitor = get_performance_monitor()
    
    # Create visualizations
    visualizer = PerformanceVisualizer(monitor)
    fig = visualizer.create_dashboard()
    
    # Generate reports
    reporter = PerformanceReporter(monitor)
    reporter.generate_html_report(Path('report.html'))
"""

# Core monitoring components
from yemen_trade_diagnostic.monitoring.performance_monitor import (
    PerformanceMonitor,
    PerformanceMetrics,
    get_performance_monitor,
    monitor_performance as monitor_performance_func
)

# Metrics collection and aggregation
from yemen_trade_diagnostic.monitoring.metrics_collector import (
    MetricsCollector,
    AggregatedMetrics
)

# Decorators for easy integration
from yemen_trade_diagnostic.monitoring.decorators import (
    monitor_performance,
    monitor_pipeline_stage,
    monitor_model_execution,
    monitor_batch_operation
)

# Visualization components
from yemen_trade_diagnostic.monitoring.visualization import (
    PerformanceVisualizer
)

# Report generation
from yemen_trade_diagnostic.monitoring.reporters import (
    PerformanceReporter
)

# Storage backends
try:
    from yemen_trade_diagnostic.monitoring.storage import (
        MetricsStorage,
        MetricsStorageBackend,
        SQLiteStorageBackend,
        JSONStorageBackend,
        create_storage_backend
    )
    STORAGE_AVAILABLE = True
except ImportError:
    STORAGE_AVAILABLE = False

# Configuration management
from yemen_trade_diagnostic.monitoring.config import (
    MonitoringConfig,
    AlertConfig,
    VisualizationConfig,
    ConfigManager,
    get_config_manager,
    get_monitoring_config,
    get_alert_config,
    get_visualization_config
)

# Version information
__version__ = '2.0.0'
__author__ = 'Yemen Trade Diagnostic Team'

# Module exports
__all__ = [
    # Core classes
    'PerformanceMonitor',
    'PerformanceMetrics',
    'MetricsCollector',
    'AggregatedMetrics',
    'PerformanceVisualizer',
    'PerformanceReporter',
    
    # Storage components
    'MetricsStorage',
    'MetricsStorageBackend',
    'SQLiteStorageBackend',
    'JSONStorageBackend',
    'create_storage_backend',
    
    # Configuration
    'MonitoringConfig',
    'AlertConfig',
    'VisualizationConfig',
    'ConfigManager',
    
    # Factory functions
    'get_performance_monitor',
    'get_config_manager',
    'get_monitoring_config',
    'get_alert_config',
    'get_visualization_config',
    
    # Decorators
    'monitor_performance',
    'monitor_pipeline_stage',
    'monitor_model_execution',
    'monitor_batch_operation',
    
    # Version info
    '__version__',
    '__author__',
    
    # Feature flags
    'STORAGE_AVAILABLE'
]

# Initialize default configuration on import
_default_config = get_monitoring_config()

# Log initialization
if _default_config.enabled:
    from yemen_trade_diagnostic.interfaces.logging_interface import get_logger
    logger = get_logger(__name__)
    logger.info(f"Performance monitoring system initialized (v{__version__})")
    
    if _default_config.storage_backend == 'file' and _default_config.storage_path:
        logger.info(f"Metrics will be stored at: {_default_config.storage_path}")
    
    if _default_config.auto_generate_reports:
        logger.info(f"Automatic reports enabled every {_default_config.report_interval_hours} hours")