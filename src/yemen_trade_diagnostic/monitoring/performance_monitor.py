"""
Performance monitoring system for Yemen Trade Diagnostic.

This module provides comprehensive performance tracking including:
- Function execution time
- Memory usage monitoring
- CPU/GPU utilization tracking
- Metrics aggregation and reporting
"""

import functools
import time
from contextlib import contextmanager
from dataclasses import dataclass, field
from datetime import datetime
from typing import Any, Callable, Dict, List, Optional, Tuple

import psutil
from yemen_trade_diagnostic.interfaces.logging_interface import get_logger
from yemen_trade_diagnostic.interfaces.hardware_interface import get_hardware_detector

logger = get_logger(__name__)

# Import storage components if available
try:
    from yemen_trade_diagnostic.monitoring.storage import (
        MetricsStorage, create_storage_backend
    )
    STORAGE_AVAILABLE = True
except ImportError:
    STORAGE_AVAILABLE = False
    logger.warning("Storage backend not available, metrics will only be stored in memory")


@dataclass
class PerformanceMetrics:
    """Container for performance metrics."""
    
    function_name: str
    start_time: datetime
    end_time: Optional[datetime] = None
    execution_time: Optional[float] = None
    memory_before: Optional[float] = None
    memory_after: Optional[float] = None
    memory_delta: Optional[float] = None
    cpu_percent: Optional[float] = None
    gpu_percent: Optional[float] = None
    exception: Optional[str] = None
    args_info: Dict[str, Any] = field(default_factory=dict)
    result_info: Dict[str, Any] = field(default_factory=dict)
    
    def calculate_metrics(self):
        """Calculate derived metrics."""
        if self.end_time and self.start_time:
            self.execution_time = (self.end_time - self.start_time).total_seconds()
        
        if self.memory_after is not None and self.memory_before is not None:
            self.memory_delta = self.memory_after - self.memory_before


class PerformanceMonitor:
    """Core performance monitoring class."""
    
    def __init__(self, 
                 enable_memory_tracking: bool = True,
                 enable_cpu_tracking: bool = True,
                 enable_gpu_tracking: bool = True,
                 storage_backend: Optional[str] = None,
                 storage_config: Optional[Dict[str, Any]] = None,
                 enable_storage: bool = True):
        """
        Initialize performance monitor.
        
        Args:
            enable_memory_tracking: Track memory usage
            enable_cpu_tracking: Track CPU utilization
            enable_gpu_tracking: Track GPU utilization if available
            storage_backend: Backend type for storing metrics ('sqlite', 'json', None)
            storage_config: Configuration for storage backend
            enable_storage: Enable persistent storage
        """
        self.enable_memory_tracking = enable_memory_tracking
        self.enable_cpu_tracking = enable_cpu_tracking
        self.enable_gpu_tracking = enable_gpu_tracking
        self.enable_storage = enable_storage and STORAGE_AVAILABLE
        
        # In-memory storage
        self.metrics_history: Dict[str, List[PerformanceMetrics]] = {}
        
        # Initialize storage backend
        self.storage: Optional['MetricsStorage'] = None
        if self.enable_storage and storage_backend:
            try:
                backend = create_storage_backend(
                    storage_backend, 
                    **(storage_config or {})
                )
                self.storage = MetricsStorage(backend)
                logger.info(f"Initialized {storage_backend} storage backend")
            except Exception as e:
                logger.warning(f"Failed to initialize storage backend: {e}")
                self.enable_storage = False
        
        # Hardware detection
        self.hardware_detector = get_hardware_detector()
        # Check if GPU is available through hardware capabilities
        self.has_gpu = False
        if enable_gpu_tracking:
            try:
                caps = getattr(self.hardware_detector, 'capabilities', {})
                self.has_gpu = caps.get('has_metal', False) or caps.get('gpu', False)
            except Exception:
                self.has_gpu = False
        
        # Process info
        self.process = psutil.Process()
    
    def _get_memory_usage(self) -> float:
        """Get current memory usage in MB."""
        if not self.enable_memory_tracking:
            return 0.0
        
        try:
            return self.process.memory_info().rss / 1024 / 1024  # Convert to MB
        except Exception as e:
            logger.warning(f"Failed to get memory usage: {e}")
            return 0.0
    
    def _get_cpu_percent(self) -> float:
        """Get current CPU usage percentage."""
        if not self.enable_cpu_tracking:
            return 0.0
        
        try:
            return self.process.cpu_percent(interval=0.1)
        except Exception as e:
            logger.warning(f"Failed to get CPU usage: {e}")
            return 0.0
    
    def _get_gpu_percent(self) -> float:
        """Get current GPU usage percentage."""
        if not self.enable_gpu_tracking or not self.has_gpu:
            return 0.0
        
        try:
            # This would integrate with hardware acceleration module
            # For now, return 0.0 as placeholder
            return 0.0
        except Exception as e:
            logger.warning(f"Failed to get GPU usage: {e}")
            return 0.0
    
    @contextmanager
    def track_execution(self, 
                       function_name: str, 
                       args_info: Optional[Dict[str, Any]] = None):
        """
        Context manager for tracking function execution.
        
        Args:
            function_name: Name of the function being tracked
            args_info: Optional information about function arguments
            
        Yields:
            PerformanceMetrics: Metrics object being populated
        """
        metrics = PerformanceMetrics(
            function_name=function_name,
            start_time=datetime.now(),
            args_info=args_info or {}
        )
        
        # Capture initial state
        metrics.memory_before = self._get_memory_usage()
        
        try:
            yield metrics
        except Exception as e:
            metrics.exception = str(e)
            raise
        finally:
            # Capture final state
            metrics.end_time = datetime.now()
            metrics.memory_after = self._get_memory_usage()
            metrics.cpu_percent = self._get_cpu_percent()
            metrics.gpu_percent = self._get_gpu_percent()
            
            # Calculate derived metrics
            metrics.calculate_metrics()
            
            # Store metrics
            self._store_metrics(metrics)
            
            # Log summary
            self._log_metrics_summary(metrics)
    
    def _store_metrics(self, metrics: PerformanceMetrics):
        """Store metrics in history and persistent storage."""
        # Store in memory
        if metrics.function_name not in self.metrics_history:
            self.metrics_history[metrics.function_name] = []
        
        self.metrics_history[metrics.function_name].append(metrics)
        
        # Store persistently if enabled
        if self.storage:
            try:
                self.storage.save_metrics(metrics)
            except Exception as e:
                logger.warning(f"Failed to save metrics to storage: {e}")
    
    def _log_metrics_summary(self, metrics: PerformanceMetrics):
        """Log a summary of the metrics."""
        if metrics.execution_time is not None:
            status = "Failed" if metrics.exception else "Success"
            memory_info = ""
            
            if metrics.memory_delta is not None:
                memory_info = f", Memory Δ: {metrics.memory_delta:.2f}MB"
            
            logger.info(
                f"Performance: {metrics.function_name} - "
                f"{status} - "
                f"Time: {metrics.execution_time:.3f}s"
                f"{memory_info}"
            )
    
    def get_metrics(self, 
                   function_name: str, 
                   last_n: Optional[int] = None) -> List[PerformanceMetrics]:
        """
        Retrieve metrics for a function.
        
        Args:
            function_name: Name of the function
            last_n: Return only the last N metrics
            
        Returns:
            List of performance metrics
        """
        metrics = self.metrics_history.get(function_name, [])
        
        if last_n is not None:
            return metrics[-last_n:]
        
        return metrics
    
    def get_summary_stats(self, function_name: str) -> Dict[str, float]:
        """
        Get summary statistics for a function.
        
        Args:
            function_name: Name of the function
            
        Returns:
            Dictionary with summary statistics
        """
        metrics = self.get_metrics(function_name)
        
        if not metrics:
            return {}
        
        execution_times = [m.execution_time for m in metrics if m.execution_time is not None]
        memory_deltas = [m.memory_delta for m in metrics if m.memory_delta is not None]
        
        stats = {}
        
        if execution_times:
            stats['execution_time_mean'] = sum(execution_times) / len(execution_times)
            stats['execution_time_min'] = min(execution_times)
            stats['execution_time_max'] = max(execution_times)
            
            # Calculate percentiles
            sorted_times = sorted(execution_times)
            stats['execution_time_p50'] = sorted_times[len(sorted_times) // 2]
            stats['execution_time_p95'] = sorted_times[int(len(sorted_times) * 0.95)]
            stats['execution_time_p99'] = sorted_times[int(len(sorted_times) * 0.99)]
        
        if memory_deltas:
            stats['memory_delta_mean'] = sum(memory_deltas) / len(memory_deltas)
            stats['memory_delta_max'] = max(memory_deltas)
        
        stats['total_executions'] = len(metrics)
        stats['failed_executions'] = sum(1 for m in metrics if m.exception is not None)
        
        return stats
    
    def clear_metrics(self, function_name: Optional[str] = None):
        """
        Clear stored metrics.
        
        Args:
            function_name: Clear metrics for specific function, or all if None
        """
        if function_name:
            self.metrics_history.pop(function_name, None)
        else:
            self.metrics_history.clear()


# Global instance for convenience
_monitor_instance: Optional[PerformanceMonitor] = None


def get_performance_monitor(
    storage_backend: str = "sqlite",
    storage_config: Optional[Dict[str, Any]] = None
) -> PerformanceMonitor:
    """
    Get or create global performance monitor instance.
    
    Args:
        storage_backend: Backend type for storage ('sqlite', 'json', None)
        storage_config: Optional storage configuration
        
    Returns:
        PerformanceMonitor instance
    """
    global _monitor_instance
    
    if _monitor_instance is None:
        _monitor_instance = PerformanceMonitor(
            storage_backend=storage_backend,
            storage_config=storage_config
        )
    
    return _monitor_instance


def monitor_performance(
    track_args: bool = False,
    track_result: bool = False,
    monitor: Optional[PerformanceMonitor] = None
):
    """
    Decorator for monitoring function performance.
    
    Args:
        track_args: Include argument information in metrics
        track_result: Include result information in metrics
        monitor: Performance monitor instance (uses global if None)
        
    Returns:
        Decorated function
    """
    def decorator(func: Callable) -> Callable:
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            # Get monitor instance
            perf_monitor = monitor or get_performance_monitor()
            
            # Prepare args info if requested
            args_info = {}
            if track_args:
                # Include basic info about arguments
                args_info['args_count'] = len(args)
                args_info['kwargs_keys'] = list(kwargs.keys())
                
                # For DataFrames, include shape
                import pandas as pd
                for i, arg in enumerate(args):
                    if isinstance(arg, pd.DataFrame):
                        args_info[f'arg_{i}_shape'] = arg.shape
            
            # Track execution
            with perf_monitor.track_execution(
                function_name=func.__name__,
                args_info=args_info
            ) as metrics:
                result = func(*args, **kwargs)
                
                # Track result info if requested
                if track_result:
                    import pandas as pd
                    if isinstance(result, pd.DataFrame):
                        metrics.result_info['shape'] = result.shape
                    elif isinstance(result, dict):
                        metrics.result_info['keys'] = list(result.keys())
                    elif isinstance(result, (list, tuple)):
                        metrics.result_info['length'] = len(result)
                
                return result
        
        return wrapper
    return decorator