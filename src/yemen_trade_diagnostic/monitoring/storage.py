"""
Persistent storage backend for monitoring metrics.

This module provides storage implementations for persisting performance metrics,
supporting multiple backends including SQLite, JSON, and CSV formats.
"""
# Standard library imports
import json
import sqlite3
from abc import ABC, abstractmethod
from datetime import datetime, timedelta
from pathlib import Path
from typing import Any, Dict, List, Optional, Union

# Third-party imports
import pandas as pd

# Project imports
from yemen_trade_diagnostic.errors import protect, OperationType
from yemen_trade_diagnostic.interfaces.logging_interface import get_logger
from yemen_trade_diagnostic.monitoring.performance_monitor import PerformanceMetrics
from yemen_trade_diagnostic.utils.file_operations import ensure_dir_exists

logger = get_logger(__name__)


class MetricsStorageBackend(ABC):
    """Abstract base class for metrics storage backends."""
    
    @abstractmethod
    def save_metrics(self, metrics: List[PerformanceMetrics]) -> bool:
        """Save metrics to storage."""
        pass
    
    @abstractmethod
    def load_metrics(
        self, 
        function_name: Optional[str] = None,
        start_time: Optional[datetime] = None,
        end_time: Optional[datetime] = None,
        limit: Optional[int] = None
    ) -> List[PerformanceMetrics]:
        """Load metrics from storage."""
        pass
    
    @abstractmethod
    def delete_metrics(
        self,
        function_name: Optional[str] = None,
        before_date: Optional[datetime] = None
    ) -> int:
        """Delete metrics from storage."""
        pass
    
    @abstractmethod
    def get_summary_stats(
        self,
        function_name: Optional[str] = None,
        time_window: Optional[timedelta] = None
    ) -> Dict[str, Any]:
        """Get summary statistics."""
        pass


class SQLiteStorageBackend(MetricsStorageBackend):
    """SQLite-based storage backend for metrics."""
    
    def __init__(self, db_path: Union[str, Path]):
        """Initialize SQLite storage backend."""
        self.db_path = Path(db_path)
        ensure_dir_exists(self.db_path.parent)
        self._initialize_database()
    
    @protect("initialize_database", OperationType.DATA_LOADING)
    def _initialize_database(self):
        """Create database tables if they don't exist."""
        with sqlite3.connect(self.db_path) as conn:
            conn.execute("""
                CREATE TABLE IF NOT EXISTS performance_metrics (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    function_name TEXT NOT NULL,
                    start_time TIMESTAMP NOT NULL,
                    end_time TIMESTAMP,
                    execution_time REAL,
                    memory_before REAL,
                    memory_after REAL,
                    memory_delta REAL,
                    cpu_percent REAL,
                    gpu_percent REAL,
                    exception TEXT,
                    args_info TEXT,
                    result_info TEXT,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            """)
            
            # Create indexes for better query performance
            conn.execute("""
                CREATE INDEX IF NOT EXISTS idx_function_name 
                ON performance_metrics(function_name)
            """)
            conn.execute("""
                CREATE INDEX IF NOT EXISTS idx_start_time 
                ON performance_metrics(start_time)
            """)
            
            conn.commit()
            logger.info(f"Initialized SQLite database at {self.db_path}")
    
    @protect("save_metrics", OperationType.DATA_LOADING)
    def save_metrics(self, metrics: List[PerformanceMetrics]) -> bool:
        """Save metrics to SQLite database."""
        if not metrics:
            return True
        
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                for metric in metrics:
                    cursor.execute("""
                        INSERT INTO performance_metrics (
                            function_name, start_time, end_time, execution_time,
                            memory_before, memory_after, memory_delta,
                            cpu_percent, gpu_percent, exception,
                            args_info, result_info
                        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                    """, (
                        metric.function_name,
                        metric.start_time,
                        metric.end_time,
                        metric.execution_time,
                        metric.memory_before,
                        metric.memory_after,
                        metric.memory_delta,
                        metric.cpu_percent,
                        metric.gpu_percent,
                        metric.exception,
                        json.dumps(metric.args_info),
                        json.dumps(metric.result_info)
                    ))
                
                conn.commit()
                logger.debug(f"Saved {len(metrics)} metrics to SQLite")
                return True
                
        except Exception as e:
            logger.error(f"Failed to save metrics to SQLite: {e}")
            return False
    
    @protect("load_metrics", OperationType.DATA_LOADING)
    def load_metrics(
        self,
        function_name: Optional[str] = None,
        start_time: Optional[datetime] = None,
        end_time: Optional[datetime] = None,
        limit: Optional[int] = None
    ) -> List[PerformanceMetrics]:
        """Load metrics from SQLite database."""
        query = "SELECT * FROM performance_metrics WHERE 1=1"
        params = []
        
        if function_name:
            query += " AND function_name = ?"
            params.append(function_name)
        
        if start_time:
            query += " AND start_time >= ?"
            params.append(start_time)
        
        if end_time:
            query += " AND start_time <= ?"
            params.append(end_time)
        
        query += " ORDER BY start_time DESC"
        
        if limit:
            query += f" LIMIT {limit}"
        
        metrics = []
        
        try:
            with sqlite3.connect(self.db_path) as conn:
                conn.row_factory = sqlite3.Row
                cursor = conn.cursor()
                cursor.execute(query, params)
                
                for row in cursor.fetchall():
                    metric = PerformanceMetrics(
                        function_name=row['function_name'],
                        start_time=datetime.fromisoformat(row['start_time']),
                        end_time=datetime.fromisoformat(row['end_time']) if row['end_time'] else None,
                        execution_time=row['execution_time'],
                        memory_before=row['memory_before'],
                        memory_after=row['memory_after'],
                        memory_delta=row['memory_delta'],
                        cpu_percent=row['cpu_percent'],
                        gpu_percent=row['gpu_percent'],
                        exception=row['exception'],
                        args_info=json.loads(row['args_info']) if row['args_info'] else {},
                        result_info=json.loads(row['result_info']) if row['result_info'] else {}
                    )
                    metrics.append(metric)
                
                logger.debug(f"Loaded {len(metrics)} metrics from SQLite")
                return metrics
                
        except Exception as e:
            logger.error(f"Failed to load metrics from SQLite: {e}")
            return []
    
    @protect("delete_metrics", OperationType.DATA_LOADING)
    def delete_metrics(
        self,
        function_name: Optional[str] = None,
        before_date: Optional[datetime] = None
    ) -> int:
        """Delete metrics from SQLite database."""
        query = "DELETE FROM performance_metrics WHERE 1=1"
        params = []
        
        if function_name:
            query += " AND function_name = ?"
            params.append(function_name)
        
        if before_date:
            query += " AND start_time < ?"
            params.append(before_date)
        
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute(query, params)
                deleted_count = cursor.rowcount
                conn.commit()
                
                logger.info(f"Deleted {deleted_count} metrics from SQLite")
                return deleted_count
                
        except Exception as e:
            logger.error(f"Failed to delete metrics from SQLite: {e}")
            return 0
    
    @protect("get_summary_stats", OperationType.COMPUTATION)
    def get_summary_stats(
        self,
        function_name: Optional[str] = None,
        time_window: Optional[timedelta] = None
    ) -> Dict[str, Any]:
        """Get summary statistics from SQLite database."""
        query = """
            SELECT 
                function_name,
                COUNT(*) as total_executions,
                SUM(CASE WHEN exception IS NOT NULL THEN 1 ELSE 0 END) as failed_executions,
                AVG(execution_time) as avg_execution_time,
                MIN(execution_time) as min_execution_time,
                MAX(execution_time) as max_execution_time,
                AVG(memory_delta) as avg_memory_delta,
                MAX(memory_delta) as max_memory_delta
            FROM performance_metrics
            WHERE 1=1
        """
        params = []
        
        if function_name:
            query += " AND function_name = ?"
            params.append(function_name)
        
        if time_window:
            cutoff_time = datetime.now() - time_window
            query += " AND start_time >= ?"
            params.append(cutoff_time)
        
        query += " GROUP BY function_name"
        
        try:
            with sqlite3.connect(self.db_path) as conn:
                conn.row_factory = sqlite3.Row
                cursor = conn.cursor()
                cursor.execute(query, params)
                
                stats = {}
                for row in cursor.fetchall():
                    stats[row['function_name']] = {
                        'total_executions': row['total_executions'],
                        'failed_executions': row['failed_executions'],
                        'success_rate': (row['total_executions'] - row['failed_executions']) / row['total_executions'] if row['total_executions'] > 0 else 0,
                        'avg_execution_time': row['avg_execution_time'],
                        'min_execution_time': row['min_execution_time'],
                        'max_execution_time': row['max_execution_time'],
                        'avg_memory_delta': row['avg_memory_delta'],
                        'max_memory_delta': row['max_memory_delta']
                    }
                
                return stats
                
        except Exception as e:
            logger.error(f"Failed to get summary stats from SQLite: {e}")
            return {}


class JSONStorageBackend(MetricsStorageBackend):
    """JSON file-based storage backend for metrics."""
    
    def __init__(self, json_path: Union[str, Path]):
        """Initialize JSON storage backend."""
        self.json_path = Path(json_path)
        ensure_dir_exists(self.json_path.parent)
        
        if not self.json_path.exists():
            self._save_data([])
    
    def _save_data(self, data: List[Dict[str, Any]]):
        """Save data to JSON file."""
        with open(self.json_path, 'w') as f:
            json.dump(data, f, indent=2, default=str)
    
    def _load_data(self) -> List[Dict[str, Any]]:
        """Load data from JSON file."""
        try:
            with open(self.json_path, 'r') as f:
                return json.load(f)
        except Exception as e:
            logger.warning(f"Failed to load JSON data: {e}")
            return []
    
    @protect("save_metrics", OperationType.DATA_LOADING)
    def save_metrics(self, metrics: List[PerformanceMetrics]) -> bool:
        """Save metrics to JSON file."""
        if not metrics:
            return True
        
        try:
            # Load existing data
            data = self._load_data()
            
            # Append new metrics
            for metric in metrics:
                data.append({
                    'function_name': metric.function_name,
                    'start_time': metric.start_time.isoformat() if metric.start_time else None,
                    'end_time': metric.end_time.isoformat() if metric.end_time else None,
                    'execution_time': metric.execution_time,
                    'memory_before': metric.memory_before,
                    'memory_after': metric.memory_after,
                    'memory_delta': metric.memory_delta,
                    'cpu_percent': metric.cpu_percent,
                    'gpu_percent': metric.gpu_percent,
                    'exception': metric.exception,
                    'args_info': metric.args_info,
                    'result_info': metric.result_info
                })
            
            # Save updated data
            self._save_data(data)
            logger.debug(f"Saved {len(metrics)} metrics to JSON")
            return True
            
        except Exception as e:
            logger.error(f"Failed to save metrics to JSON: {e}")
            return False
    
    @protect("load_metrics", OperationType.DATA_LOADING)
    def load_metrics(
        self,
        function_name: Optional[str] = None,
        start_time: Optional[datetime] = None,
        end_time: Optional[datetime] = None,
        limit: Optional[int] = None
    ) -> List[PerformanceMetrics]:
        """Load metrics from JSON file."""
        data = self._load_data()
        metrics = []
        
        for item in data:
            # Apply filters
            if function_name and item.get('function_name') != function_name:
                continue
            
            item_start_time = datetime.fromisoformat(item['start_time']) if item.get('start_time') else None
            
            if start_time and item_start_time and item_start_time < start_time:
                continue
            
            if end_time and item_start_time and item_start_time > end_time:
                continue
            
            # Create metric object
            metric = PerformanceMetrics(
                function_name=item['function_name'],
                start_time=item_start_time,
                end_time=datetime.fromisoformat(item['end_time']) if item.get('end_time') else None,
                execution_time=item.get('execution_time'),
                memory_before=item.get('memory_before'),
                memory_after=item.get('memory_after'),
                memory_delta=item.get('memory_delta'),
                cpu_percent=item.get('cpu_percent'),
                gpu_percent=item.get('gpu_percent'),
                exception=item.get('exception'),
                args_info=item.get('args_info', {}),
                result_info=item.get('result_info', {})
            )
            metrics.append(metric)
        
        # Sort by start time (most recent first)
        metrics.sort(key=lambda m: m.start_time or datetime.min, reverse=True)
        
        # Apply limit
        if limit:
            metrics = metrics[:limit]
        
        logger.debug(f"Loaded {len(metrics)} metrics from JSON")
        return metrics
    
    @protect("delete_metrics", OperationType.DATA_LOADING)
    def delete_metrics(
        self,
        function_name: Optional[str] = None,
        before_date: Optional[datetime] = None
    ) -> int:
        """Delete metrics from JSON file."""
        data = self._load_data()
        original_count = len(data)
        
        # Filter data
        filtered_data = []
        for item in data:
            # Skip if matches deletion criteria
            if function_name and item.get('function_name') == function_name:
                continue
            
            if before_date:
                item_start_time = datetime.fromisoformat(item['start_time']) if item.get('start_time') else None
                if item_start_time and item_start_time < before_date:
                    continue
            
            filtered_data.append(item)
        
        # Save filtered data
        self._save_data(filtered_data)
        
        deleted_count = original_count - len(filtered_data)
        logger.info(f"Deleted {deleted_count} metrics from JSON")
        return deleted_count
    
    @protect("get_summary_stats", OperationType.COMPUTATION)
    def get_summary_stats(
        self,
        function_name: Optional[str] = None,
        time_window: Optional[timedelta] = None
    ) -> Dict[str, Any]:
        """Get summary statistics from JSON data."""
        metrics = self.load_metrics(function_name=function_name)
        
        # Apply time window filter
        if time_window:
            cutoff_time = datetime.now() - time_window
            metrics = [m for m in metrics if m.start_time and m.start_time >= cutoff_time]
        
        # Group by function name
        stats = {}
        from collections import defaultdict
        grouped = defaultdict(list)
        
        for metric in metrics:
            grouped[metric.function_name].append(metric)
        
        for func_name, func_metrics in grouped.items():
            execution_times = [m.execution_time for m in func_metrics if m.execution_time is not None]
            memory_deltas = [m.memory_delta for m in func_metrics if m.memory_delta is not None]
            failed_count = sum(1 for m in func_metrics if m.exception is not None)
            
            stats[func_name] = {
                'total_executions': len(func_metrics),
                'failed_executions': failed_count,
                'success_rate': (len(func_metrics) - failed_count) / len(func_metrics) if func_metrics else 0,
                'avg_execution_time': sum(execution_times) / len(execution_times) if execution_times else 0,
                'min_execution_time': min(execution_times) if execution_times else 0,
                'max_execution_time': max(execution_times) if execution_times else 0,
                'avg_memory_delta': sum(memory_deltas) / len(memory_deltas) if memory_deltas else 0,
                'max_memory_delta': max(memory_deltas) if memory_deltas else 0
            }
        
        return stats


class MetricsStorage:
    """Main metrics storage manager with retention policies."""
    
    def __init__(
        self,
        backend: MetricsStorageBackend,
        retention_days: int = 30,
        auto_cleanup: bool = True
    ):
        """
        Initialize metrics storage manager.
        
        Args:
            backend: Storage backend implementation
            retention_days: Number of days to retain metrics
            auto_cleanup: Automatically clean up old metrics
        """
        self.backend = backend
        self.retention_days = retention_days
        self.auto_cleanup = auto_cleanup
        
        if auto_cleanup:
            self._cleanup_old_metrics()
    
    @protect("cleanup_old_metrics", OperationType.DATA_LOADING)
    def _cleanup_old_metrics(self):
        """Clean up metrics older than retention period."""
        cutoff_date = datetime.now() - timedelta(days=self.retention_days)
        deleted_count = self.backend.delete_metrics(before_date=cutoff_date)
        
        if deleted_count > 0:
            logger.info(f"Cleaned up {deleted_count} metrics older than {self.retention_days} days")
    
    def save_metrics(self, metrics: Union[PerformanceMetrics, List[PerformanceMetrics]]) -> bool:
        """Save metrics to storage."""
        if isinstance(metrics, PerformanceMetrics):
            metrics = [metrics]
        
        return self.backend.save_metrics(metrics)
    
    def load_metrics(self, **kwargs) -> List[PerformanceMetrics]:
        """Load metrics from storage."""
        return self.backend.load_metrics(**kwargs)
    
    def get_summary_stats(self, **kwargs) -> Dict[str, Any]:
        """Get summary statistics."""
        return self.backend.get_summary_stats(**kwargs)
    
    def export_to_dataframe(
        self,
        function_name: Optional[str] = None,
        time_window: Optional[timedelta] = None
    ) -> pd.DataFrame:
        """Export metrics to pandas DataFrame for analysis."""
        metrics = self.load_metrics(function_name=function_name)
        
        if time_window:
            cutoff_time = datetime.now() - time_window
            metrics = [m for m in metrics if m.start_time and m.start_time >= cutoff_time]
        
        # Convert to DataFrame
        data = []
        for metric in metrics:
            data.append({
                'function_name': metric.function_name,
                'start_time': metric.start_time,
                'end_time': metric.end_time,
                'execution_time': metric.execution_time,
                'memory_before': metric.memory_before,
                'memory_after': metric.memory_after,
                'memory_delta': metric.memory_delta,
                'cpu_percent': metric.cpu_percent,
                'gpu_percent': metric.gpu_percent,
                'success': metric.exception is None,
                'exception': metric.exception
            })
        
        df = pd.DataFrame(data)
        
        # Add derived columns if we have data
        if not df.empty:
            df['date'] = pd.to_datetime(df['start_time']).dt.date
            df['hour'] = pd.to_datetime(df['start_time']).dt.hour
        
        return df


# Factory function for creating storage backends
def create_storage_backend(
    backend_type: str = "sqlite",
    **kwargs
) -> MetricsStorageBackend:
    """
    Create a storage backend instance.
    
    Args:
        backend_type: Type of backend ("sqlite", "json")
        **kwargs: Backend-specific configuration
        
    Returns:
        MetricsStorageBackend instance
    """
    if backend_type == "sqlite":
        db_path = kwargs.get('db_path', 'output/metrics/performance_metrics.db')
        return SQLiteStorageBackend(db_path)
    
    elif backend_type == "json":
        json_path = kwargs.get('json_path', 'output/metrics/performance_metrics.json')
        return JSONStorageBackend(json_path)
    
    else:
        raise ValueError(f"Unknown backend type: {backend_type}")