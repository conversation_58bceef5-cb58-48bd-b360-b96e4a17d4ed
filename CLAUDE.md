# Yemen Trade Diagnostic - Project Documentation

> **Last Updated**: May 24, 2025  
> **Version**: 2.7.2  
> **Status**: Phase 2.7 Near Completion (95% Complete)

## Table of Contents
1. [Project Overview](#project-overview)
2. [Quick Start](#quick-start)
3. [Architecture](#architecture)
4. [Development Guidelines](#development-guidelines)
5. [API Reference](#api-reference)
6. [Testing](#testing)
7. [Deployment](#deployment)
8. [Project Status](#project-status)
9. [Contributing](#contributing)

---

## Project Overview

Yemen Trade Diagnostic is a comprehensive data analysis system for international trade patterns, utilizing BACI trade data and World Bank indicators to provide insights into Yemen's trade dynamics.

### Key Features
- **High-Performance Data Processing**: Hardware-accelerated computations with Apple Silicon optimization
- **Robust Error Handling**: Unified error system with circuit breakers and graceful degradation
- **Comprehensive Monitoring**: Real-time performance tracking and analytics
- **Modular Architecture**: Clean separation of concerns with well-defined interfaces
- **Extensive Testing**: 90%+ code coverage with unit, integration, and E2E tests

### Technology Stack
- **Language**: Python 3.9+
- **Core Libraries**: pandas, numpy, scikit-learn
- **Visualization**: Plotly, Matplotlib
- **Hardware Acceleration**: Apple Metal Performance Shaders (MPS)
- **Testing**: pytest, coverage
- **Documentation**: Sphinx, mkdocs

---

## Quick Start

### Installation

```bash
# Clone the repository
git clone https://github.com/your-org/yemen-trade-diagnostic.git
cd yemen-trade-diagnostic

# Create virtual environment
python3.9 -m venv venv_3.9
source venv_3.9/bin/activate

# Install dependencies
pip install -r requirements.txt
pip install -r requirements-dev.txt
```

### Basic Usage

```python
# Run a single pipeline
from yemen_trade_diagnostic.cli import main
main(['pipeline', 'rca', '--year', '2023'])

# Use the unified data loader
from yemen_trade_diagnostic.data import load_data, DataSource
data = load_data(DataSource.BACI, year=2023)

# Apply error protection
from yemen_trade_diagnostic.errors import protect, OperationType

@protect("data_analysis", OperationType.COMPUTATION)
def analyze_trade_data(df):
    return df.groupby('product_code').sum()
```

### Command Line Interface

```bash
# Run all pipelines for a year
python scripts/utilities/run_pipelines.py --year 2023

# Run specific pipeline
python -m yemen_trade_diagnostic.cli pipeline rca --year 2023

# Run tests
python scripts/testing/run_all_tests.py --coverage

# Monitor performance
python scripts/monitoring/performance_monitor.py
```

---

## Architecture

### System Design

```
src/yemen_trade_diagnostic/
├── data/              # Unified data loading system
├── models/            # Economic models (RCA, HHI, etc.)
├── pipelines/         # Data processing pipelines
├── interfaces/        # System interfaces and contracts
├── hardware/          # Hardware acceleration layer
├── errors/            # Unified error handling
├── monitoring/        # Performance monitoring
├── visualization/     # Chart generation (v2)
└── utils/             # Consolidated utilities
```

### Key Components

#### 1. Unified Data System
- Single entry point: `load_data()`
- Automatic caching and optimization
- Hardware acceleration support
- Integrated error handling

#### 2. Error Handling System v2.0
- **Decorator-based protection**: `@protect()`
- **Context managers**: `error_context()`
- **Operation-specific handling**: Different strategies for different operations
- **Hardware-aware**: Automatic optimization based on available hardware

#### 3. Performance Monitoring
- **Function-level tracking**: `@monitor_performance()`
- **Pipeline stage monitoring**: `@monitor_pipeline_stage()`
- **Real-time dashboards**: Interactive visualization of metrics
- **Multi-format reporting**: HTML, PDF, Excel, Markdown

#### 4. Hardware Acceleration
- **Apple Silicon optimization**: Metal Performance Shaders integration
- **Automatic fallback**: CPU fallback for unsupported operations
- **Memory optimization**: Efficient memory pooling and management
- **Workload analysis**: Automatic selection of optimal execution path

---

## Development Guidelines

### Code Standards

#### 1. Import Convention
```python
# Standard library imports
import os
from pathlib import Path

# Third-party imports
import pandas as pd
import numpy as np

# Project imports (always use absolute imports)
from yemen_trade_diagnostic.data import load_data
from yemen_trade_diagnostic.errors import protect, OperationType
```

#### 2. Error Handling
```python
# Always use the unified error system
@protect("operation_name", OperationType.COMPUTATION)
def process_data(df: pd.DataFrame) -> pd.DataFrame:
    """Process trade data with automatic error protection."""
    return df.groupby('product_code').sum()

# For complex operations, use context managers
def complex_pipeline(year: int) -> Dict[str, Any]:
    with error_context("data_loading", OperationType.DATA_LOADING):
        data = load_data(DataSource.BACI, year=year)
    
    with error_context("processing", OperationType.COMPUTATION):
        result = process_data(data)
    
    return result
```

#### 3. Performance Monitoring
```python
@monitor_performance(track_args=True, log_threshold=1.0)
def expensive_computation(data: pd.DataFrame) -> pd.DataFrame:
    """Computation with automatic performance tracking."""
    return data.apply(complex_function)
```

#### 4. Type Hints
```python
# Always use type hints for function signatures
def calculate_trade_balance(
    exports: pd.DataFrame,
    imports: pd.DataFrame,
    year: int
) -> Dict[str, float]:
    """Calculate trade balance with full type annotations."""
    pass
```

### File Organization

#### Utilities (Consolidated)
- **column_mapping.py**: All column mapping operations
- **file_operations.py**: All file I/O operations
- **memory_management.py**: Memory optimization utilities
- **config.py**: Configuration management

#### Deprecated Files (Removed)
- ~~column_mapper.py~~ → Merged into column_mapping.py
- ~~file_utils.py~~ → Merged into file_operations.py
- ~~output_utils.py~~ → Merged into file_operations.py
- ~~memory_optimizer.py~~ → Merged into memory_management.py
- ~~memory_utils.py~~ → Merged into memory_management.py
- ~~error_interface.py~~ → Use errors module directly

### Git Workflow

```bash
# Feature branches
git checkout -b phase2/feature-name

# Commit messages (conventional commits)
git commit -m "feat(module): Add new feature

- Detailed description
- Breaking changes noted with BREAKING CHANGE:
- Closes #123"

# Types: feat, fix, docs, style, refactor, test, chore
```

---

## API Reference

### Data Loading
```python
from yemen_trade_diagnostic.data import load_data, DataSource

# Load BACI trade data
baci_data = load_data(DataSource.BACI, year=2023)

# Load World Bank data
wb_data = load_data(DataSource.WORLD_BANK, indicator="GDP")

# Load with custom parameters
data = load_data(
    source=DataSource.YEMEN_EXPORTS,
    year=2023,
    use_cache=True,
    hardware_accelerate=True
)
```

### Error Protection
```python
from yemen_trade_diagnostic.errors import (
    protect, error_context, OperationType,
    get_health_status, get_performance_metrics
)

# Function protection
@protect("my_operation", OperationType.COMPUTATION)
def my_function():
    pass

# Context-based protection
with error_context("loading", OperationType.DATA_LOADING):
    data = load_data()

# Monitor system health
health = get_health_status()
metrics = get_performance_metrics()
```

### Performance Monitoring
```python
from yemen_trade_diagnostic.monitoring.decorators import (
    monitor_performance,
    monitor_pipeline_stage
)

@monitor_performance(track_args=True)
def process_data(df):
    pass

@monitor_pipeline_stage("validation")
def validate_data(df):
    pass
```

---

## Testing

### Test Structure
```
tests/
├── unit/              # Fast, isolated tests
├── integration/       # Component interaction tests
├── performance/       # Performance benchmarks
├── e2e/              # End-to-end workflows
└── fixtures/         # Shared test data
```

### Running Tests
```bash
# Run all tests with coverage
python scripts/testing/run_all_tests.py --coverage

# Run specific test categories
python scripts/testing/run_all_tests.py --unit
python scripts/testing/run_all_tests.py --integration
python scripts/testing/run_all_tests.py --performance

# Run with pytest directly
pytest tests/unit -v --cov=yemen_trade_diagnostic
```

### Test Markers
- `@pytest.mark.unit`: Unit tests
- `@pytest.mark.integration`: Integration tests
- `@pytest.mark.performance`: Performance tests
- `@pytest.mark.hardware`: Hardware acceleration tests
- `@pytest.mark.slow`: Tests taking >5 seconds

---

## Deployment

### Production Configuration
```python
# config/production.py
HARDWARE_ACCELERATION = True
CACHE_ENABLED = True
ERROR_REPORTING = "sentry"
LOG_LEVEL = "WARNING"
```

### Docker Deployment
```bash
# Build image
docker build -t yemen-trade-diagnostic:latest .

# Run container
docker run -p 8000:8000 yemen-trade-diagnostic:latest
```

### Health Checks
```bash
# Check system health
python scripts/testing/run_health_check.py

# Monitor performance
python scripts/monitoring/hardware_acceleration_monitor.py
```

---

## Project Status

### Phase 2 Progress Overview

| Phase | Status | Completion | Description |
|-------|--------|------------|-------------|
| **2.1** | ✅ Complete | 100% | Performance Monitoring System |
| **2.2** | ✅ Complete | 100% | Enhanced Error Handling |
| **2.3** | ✅ Complete | 100% | Test Reorganization |
| **2.4** | ✅ Complete | 100% | RCA Model Consolidation |
| **2.5** | 📅 Planned | 0% | Documentation Enhancement |
| **2.6** | ✅ Complete | 100% | Data System Streamline |
| **2.7** | 🚧 In Progress | 95% | Module Review & Cleanup |

### Recent Achievements (May 24, 2025)

#### ✅ Critical Utility Consolidation
- **Column Mapping**: Merged `column_mapper.py` → `column_mapping.py`
- **File Operations**: Consolidated `file_utils.py` + `output_utils.py` → `file_operations.py`  
- **Memory Management**: Unified `memory_optimizer.py` + `memory_utils.py` → `memory_management.py`
- **Error Interface**: Removed deprecated `error_interface.py`
- **Publishing Cleanup**: Minimized `publishing_config.json`, removed obsolete references

#### ✅ Monitoring Storage Backend
- **SQLite Backend**: Persistent storage for performance metrics with automatic retention
- **JSON Backend**: Alternative lightweight storage option
- **Retention Policies**: Automatic cleanup of old metrics (30-day default)
- **Export Capabilities**: Convert metrics to DataFrame for analysis

#### ✅ Hardware Fallback System
- **Universal Accelerator**: Works seamlessly across all platforms
- **Platform Detection**: Automatic detection of x86, ARM, Apple Silicon capabilities
- **Optimized Fallbacks**: Uses MKL, OpenBLAS, Numba based on availability
- **Strategy Selection**: Automatic selection of best approach based on workload
- **Impact**: 100% platform coverage with optimal performance on each system

#### ✅ Utility Registry System
- **Central Registry**: Comprehensive tracking of all utility functions
- **Duplicate Detection**: Automatic identification of duplicate implementations
- **Function Discovery**: Auto-discovery and registration of utilities
- **Category Organization**: 126 functions organized across 7 categories
- **Report Generation**: Automated documentation of utility landscape

### Current Focus

#### 🚧 Phase 2.7: Module Review & Cleanup (95% Complete)
- [x] AI-powered module review
- [x] Critical utility consolidation
- [x] Deprecated code removal
- [x] Memory utility consolidation
- [x] Publishing system cleanup
- [x] Monitoring storage backend implementation
- [x] Hardware fallback system for all platforms
- [x] Utility registry implementation
- [ ] Legacy visualization cleanup

### Next Priorities
1. **Test Suite**: Fix numpy compatibility issues
2. **Legacy Cleanup**: Remove old visualization code  
3. **Documentation**: Update Phase 2.5 documentation
4. **Final Testing**: Complete E2E test suite

### Performance Metrics
- **Response Time**: 32.5% faster than V1
- **Error Rate**: 33.3% reduction
- **Memory Usage**: 37% reduction
- **Test Coverage**: 87% (target: >90%)
- **Code Quality**: 7.5/10 (production-ready)

---

## Contributing

### Development Setup
1. Fork the repository
2. Create a feature branch
3. Make your changes following guidelines
4. Add/update tests
5. Update documentation
6. Submit pull request

### Code Review Checklist
- [ ] Tests pass locally
- [ ] Type hints added
- [ ] Documentation updated
- [ ] No duplicate code introduced
- [ ] Error handling implemented
- [ ] Performance impact considered

### Getting Help
- **Documentation**: See `/docs` directory
- **Issues**: GitHub issue tracker
- **Discussions**: GitHub discussions
- **Email**: <EMAIL>

---

## Appendix

### Useful Commands Reference
```bash
# Development
source venv_3.9/bin/activate
python scripts/shell/run_with_python39_path.sh script.py

# Testing
python scripts/testing/run_all_tests.py --coverage
python scripts/testing/run_health_check.py

# Monitoring
python scripts/monitoring/performance_monitor.py
python scripts/monitoring/generate_nightly_report.py

# Utilities
python scripts/utilities/run_pipelines.py
python scripts/utilities/clear_cache.py
```

### Environment Variables
```bash
export PYTHONPATH=/path/to/yemen-trade-diagnostic/src
export YTD_ENV=development
export YTD_CACHE_DIR=/tmp/ytd_cache
export YTD_LOG_LEVEL=DEBUG
```

### Troubleshooting
See `docs/troubleshooting.md` for common issues and solutions.

---

*This document is maintained as the primary source of truth for the Yemen Trade Diagnostic project.*