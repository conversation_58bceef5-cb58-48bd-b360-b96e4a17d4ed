{"performance_monitoring": {"enabled": true, "storage_backend": "sqlite", "storage_config": {"db_path": "output/metrics/performance_metrics.db", "json_path": "output/metrics/performance_metrics.json"}, "retention_days": 30, "auto_cleanup": true, "tracking": {"memory": true, "cpu": true, "gpu": true, "args": false, "result": false}}, "metrics_collection": {"aggregation_interval": 300, "batch_size": 100, "export_formats": ["csv", "json", "html"]}, "alerts": {"enabled": false, "thresholds": {"execution_time_p95": 5.0, "memory_delta_max": 1000.0, "error_rate": 0.1}}}